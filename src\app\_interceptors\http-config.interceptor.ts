import { Injectable } from '@angular/core';
import {
  HttpRequest,
  HttpHandler,
  HttpEvent,
  HttpInterceptor,
  HttpHeaders,
  HttpXsrfTokenExtractor,
  HttpResponse,
  HttpErrorResponse,
} from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { Router } from '@angular/router';
import { catchError, map } from 'rxjs/operators';
import { Auth0Service } from '../services/auth0.service';
import { environment } from 'src/environments/environment';

@Injectable()
export class HttpConfigInterceptor implements HttpInterceptor {
  constructor(
    private tokenExtractor: HttpXsrfTokenExtractor,
    private router: Router,
    private auth0: Auth0Service
  ) {}
  user;
  intercept(
    request: HttpRequest<any>,
    next: HttpHandler
  ): Observable<HttpEvent<any>> {
    // Set Credentials (<PERSON>ie)
    request.clone({
      withCredentials: true,
    });
    if (this.tokenExtractor.getToken()) {
      let headers: HttpHeaders;
      if (request.headers.has('Content-Range')) {
        headers = new HttpHeaders({
          'Content-Range': request.headers.get('Content-Range'),
        });
      } else {
        if (!request.url.startsWith('https://storage.googleapis.com/')) {
          headers = new HttpHeaders({
            'X-Requested-With': 'XMLHttpRequest',
            'x-CSRFToken': this.tokenExtractor.getToken(),
          });
        }
      }
      request = request.clone({
        headers,
      });
    }
    // || !navigator.onLine
    return next.handle(request).pipe(
      map((response: HttpResponse<any>) => response),
      catchError((error: HttpResponse<HttpErrorResponse>) => {
        if (!navigator.onLine) {
          this.router.navigate(['/offline'], {
            queryParams: { from: this.router.url },
          });
        }
        if (error.status === 403 || error.status === 401) {
          if (localStorage.getItem('user')) {
            this.user = JSON.parse(localStorage.getItem('user'));
          }
          this.auth0.logUserOut();
          localStorage.clear();
        }

        return throwError(error);
      })
    );
  }
}
