@import "../../../styles/variables";

.help-wrapper {
  background-color: #fff;
  padding: 26px;
  // width: 100%;
  height: 100%;
  margin-left: 75px;
  // margin-top: 70px;
  .help-head {
    margin-top: 70px;
    .help-page-client-logo {
      img {
      width: 50px;
      height: 40px;
      object-fit: contain;
      }
    }
  }
  .help-body {
    margin-top: 40px;
    .form-field-heading {
      font: normal normal bold 14px/21px Poppins;
    }
    textarea {
      height: 120px;
    }
  }
  .btn-group {
    button {
      font-family: $site-font;
      font-style: normal;
      font-weight: 600;
      font-size: 14px;
      line-height: 21px;
      padding: 10px;
      text-align: center;
      width: 100%;

    }
   
  }
}
// to avoid red border
::ng-deep .mat-form-field-appearance-outline.mat-form-field-invalid.mat-form-field-invalid .mat-form-field-outline-thick{
  color: #E6E8F0!important;
  opacity: 0.8!important;
}