import { ComponentFixture, TestBed } from '@angular/core/testing';

import { ReferenceUrlDialogComponent } from './reference-url-dialog.component';

describe('ReferenceUrlDialogComponent', () => {
  let component: ReferenceUrlDialogComponent;
  let fixture: ComponentFixture<ReferenceUrlDialogComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ ReferenceUrlDialogComponent ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(ReferenceUrlDialogComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
