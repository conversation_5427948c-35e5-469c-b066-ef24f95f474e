@import "../../../styles/variables";

.wrapper {
  .table-wrapper {
    margin-top: 30px;
    .card-wrapper {
      margin-right: 40px;
    }
  }
}

.setting-card {
  background: $theme-white;
  border-radius: 4px;
  flex-basis: 100%;
  div {
    justify-content: space-between;
    height: 90px;
    padding: 10px 0;
  span {
    font-family: $site-font;
    font-style: normal;
    color: $theme-black;
  }
  .count {
    font-weight: bold;
    font-size: 32px;
  }
  .stat-name {
    font-weight: normal;
    font-size: 14px;
    color: #3b3e48;
  }
}
}
.chart-container {
  margin-top: 10px;
  p {
    font-family: $site-font;
    font-style: normal;
    font-weight: 600;
    font-size: 18px;
    line-height: 27px;
  }
  .chart-filter {
    margin-right: 60px;
    width: fit-content;
    height: 40px;
    border: 1px solid #c1c4d6;
    box-sizing: border-box;
    border-radius: 4px;
    .mat-select {
      position: absolute;
      margin-top: -5px;
      color: $theme-white;
      padding-left: 10px;
      width: 90%;
    }
  }
}
.chart {
  width: 95%;
  height: 100%;
  background-color: $theme-white;
}
// :host::ng-deep .mat-select-placeholder {
//   color: $theme-button;
// }
