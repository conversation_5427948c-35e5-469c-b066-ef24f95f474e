/* You can add global styles to this file, and also import other style files */
@import "./variables";
html,
body {
  height: 100%;
}
body {
  margin: 0;
  font-family: $site-font;
}

a {
  cursor: pointer;
  text-decoration: none;
  font-weight: 700;
}

// mat chips
.mat-chip {
  background: #f4f9ff 0% 0% no-repeat padding-box;
  border-radius: 16px;
  opacity: 1;
  margin-right: 10px;
}

// filter
mat-menu {
  .mat-menu:hover {
    background: $theme-hover !important;
    color: $theme-blue !important;
  }
}
mat-option {
  // color: $tabel-data;
  text-align: left;
  padding: 25px 10px;
  border-bottom: 1px solid #e6e8f0;
  .range-category {
    font: normal normal normal 14px/21px $site-font;
    .date-range {
      font: normal normal 300 12px/18px $site-font;
    }
  }
}
// .mat-select-panel {
//   height: fit-content;
//   overflow: auto !important;
//   max-width: 323px !important;
//   margin-top: 17%;
//   border-radius: 20px;
// }
.mat-select-panel {
  margin-top: 40px
}

// mat table
// table container
.table-section {
  background: $theme-white;
  border-radius: 4px;
  margin: 0px 0 10% 14px;
  width: 95% !important;
}
mat-tab-group {
  width: 100%;
}

.table-scroll::-webkit-scrollbar {
  display: none;
}

table {
  border-radius: 4px;
  width: 100%;
  height: 100%;
}
.mat-cell {
  color: #646464;
  font-weight: 400;
}
mat-tab-group {
  width: 100%;
  mat-tab {
    position: fixed;
  }
}

th {
  background-color: $tabel-header;
  // border-radius: 4px 4px 0px 0px;
  font-family: $site-font;
  font-style: normal;
  font-weight: 600;
  font-size: 16px;
  line-height: 24px;
  color: $theme-black;
  opacity: 1;
}
td {
  color: $tabel-data;
  width: auto;
  font-family: $site-font;
  font-weight: normal;
  .sub-text {
    font-family: $site-font;
    font-style: normal;
    font-weight: 300;
    font-size: 12px;
    line-height: 18px;
    color: #747884;
  }
}
// toolbar section wrapper
.wrapper {
  margin-right: 10px;
  margin-left: 76px;
  margin-top: 70px;
  background-color: #f4f6fa;

  .filter-container {
    height: fit-content;;
    display: flex;
    align-items: center;
    z-index: 1;
    position: sticky;
    top: 70px;
    width: 100%;
    border-bottom: 2px solid #edeff5;
    background-color: $theme-background;
    opacity: 1;
    .filter-head {
      width: 95%;
      margin-top: 15px;
      margin-left: 10px;
      margin-bottom: 15px;
      .search-container {
        text-align: left;
        font: normal normal 600 16px/40px $site-font;
        letter-spacing: 0.32px;
        color: #3b3e48;
        opacity: 1;
        .search-filter {
          width: 440px;
          height: 40px;
          border: 1px solid #c1c4d6;
          box-sizing: border-box;
          border-radius: 4px;
          input {
            width: 90%;
            height: 19px;
            margin-left: 5px;
            margin-top: -12px;
            margin-bottom: 10px;
          }
          .search-icon {
            cursor: pointer;
            font-size: 30px;
            color: #b6b6b6;
            padding: 5px;
          }
          .remove-icon {
            color: #b6b6b6;
            font-size: 24px;
            bottom: 5px;
            cursor: pointer;
            position: relative;
            right: 5px;
          }
          .cancel-search {
            bottom: 6px;
            position: relative;
            cursor: pointer;
          }
        }
      }
      // upload batch
      .btn-container {
        // margin-right: 100px;
        .upload-btn {
          width: auto;
          height: 40px;
          border-radius: 4px;
          font-weight: 600;
        }
        .view-messages-icon {
          color: #3b3e48;
          cursor: pointer;
          display: flex;
          height: 40px;
          align-items: center;
          .dot {
            position: relative;
            height: 8px;
            width: 8px;
            background-color: #d30505;
            border-radius: 50%;
            top: 6px;
            left: 18px;
          }
        }
        //date filter
        .date-range-filter {
          width: 340px;
          height: 40px;
          border: 1px solid #c1c4d6;
          box-sizing: border-box;
          border-radius: 4px;
          mat-select {
            padding: 2px 0;
            // width is 40px (margin-left) less than total width
            width: 300px;
            margin-left: 40px;
            margin-top: -1px;
            font: normal normal normal 14px/21px $site-font;
            .mat-select-placeholder {
              color: #3b3e48;
              padding-left: 35px;
            }
            .mat-select-arrow-wrapper {
              padding-right: 5px;
              .mat-select-arrow {
                color: #c1c4d6;
              }
            }
          }
          .mat-form-field-flex {
            border-radius: 4px;
            .mat-form-field-infix {
              border: 0px solid #e6e8f0;
              border-radius: 5px;
              width: 100%;
            }
          }
          .date-range-icon {
            margin-top: -30px;
            padding: 5px;
          }
        }
      }
      .reset-btn {
        height: 40px;
      }
    }
  }

  //table
  .table-wrapper {
    z-index: 0;
    margin-left: 20px;
    margin-top: 10px;
    margin-bottom: 40px;
    .batch-count {
      width: max-content;
      height: 24px;
      background: #e6e8f0;
      border-radius: 12px;
      padding: 10px;
    }

    .copy-icon {
      cursor: pointer;
      margin-top: 5px;
      margin-left: 1px;
      font-size: 15px;
      color: #3b3e48;
    }
    .batch-date {
      margin-top: 10px;
      font-style: normal;
      font-weight: 300;
      font-size: 12px;
      line-height: 18px;
      color: #222329;
      justify-content: space-between;
      max-width: 200px;
      .batch-info {
        cursor: pointer;
        // margin-top: 5px;
        margin-left: 3px;
        font-size: 17px;
        color: #3b3e48;
      }
      .gray-theme {
        color: #959191;
      }
    }
    .batch-name {
      margin-top: 10px;
      font-size: 14px;
      line-height: 21px;
      font-style: normal;
      font-family: $site-font;
    }
    .description {
      margin-top: -10px;
      font-style: normal;
      font-weight: 300;
      font-size: 12px;
      line-height: 18px;
    }
    .tags-container {
      max-width: 260px;
      padding: 5px;
      .label-chip {
      width: max-content;
        font-style: normal;
        font-weight: normal;
        font-size: 10px;

        .remove-label-chip {
          font-size: 12px;
          width: max-content;
          height: 16px;
          // color: #575454;
          cursor: pointer;
          margin-left: 2px;
          margin-top: 5px;
        }
      }
    }
    mat-chip-list:focus {
      outline: 0;
    }
    .add-label-btn {
      cursor: pointer;
      box-sizing: border-box;
      border-radius: 20.5px;
      background-color: white;
      font-size: 10px;
      line-height: 15px;
      height: 30px;
      // margin-bottom: 5px;
      text-align: left;
      width: max-content;
      padding: 10px 10px;
      font-family: $site-font;
      .add-label-icon {
        // margin-left: -5px;
        font-size: 15px;
        width: 15px;
        height: 15px;
      }
    }
    // progress bar div
    .progress-wrapper {
      width: 80%;
      align-items: start;
      mat-progress-bar {
        background-color: red;
        justify-content: center;
        border-radius: 5px;
        opacity: 1;
      }
      .progress-bar-value {
        text-align: left;
        letter-spacing: 0px;
        color: #3b3e48;
        opacity: 1;
        margin-top: -10px;
        font-weight: 400;
        margin-bottom: 0;
      }
    }

    // tabel action column icons
    .action-btn {
      width: fit-content;
      align-items: center;
      // padding: 10px;
      button {
        padding: 2px;
        width: 100%;
        font-family: $site-font;
        border-radius: 4px;
        font-style: normal;
        font-weight: 500;
        font-size: 12px;
        line-height: 21px;
        margin-bottom: 10px;
        cursor: pointer;
      }

      .batch-download {
        color: $theme-white;
        padding: 5px 10px;
      }
    }
    .batch-cancelled {
      width: fit-content;
      color: $theme-red;
      font-size: $site-font;
      font-weight: normal;
      font-size: 14px;
      line-height: 21px;
    }
  }
}
.chip-container {
  width: 90%;
  .panel-chip {
    margin-right: 10px;
    width: auto;
    padding: 10px;
    height: 40px;
    border-radius: 25px;
  }
}
//tag
.tag-color-picker {
  // user-select: none;
  margin-right: 10px;
  width: 25px;
  height: 5px;
  border-radius: 4px;
  background-color: #e6e8f0;
  padding: 10px;
  .iccon {
    position: absolute;
    margin-left: -10px;
    color: rgba(252, 252, 252, 0);
  }
}

.mat-chip-selected {
  .iccon {
    color: #1e2540;
  }
  color: rgba(180, 180, 180, 0.466);
}
// mat stepper
::ng-deep.mat-step-header.mat-step-icon {
  display: none;
}
.mat-step-label {
  white-space: normal !important;
}

.batch-id {
  margin-top: 20px;
  text-align: center;
  font-weight: 600;
  font-size: 14px;
  line-height: 21px;
  text-decoration-line: underline;
  width: 58px;
  height: 21px;
}

// id highlight in log
.batch-log-id {
  font-weight: 600;
}

// ::ng-deep .mat-tab-labels .mat-tab-label-active {
//   // width: 112px;
//   opacity: 1;
//   font-family: $site-font;
// }

// ::ng-deep .mat-tab-label {
//   font-family: $site-font;
// }

::ng-deep .mat-tab-group.mat-primary .mat-ink-bar {
  height: 4px;
  // width: 100%;
  border-radius: 4px 4px 0px 0px;
}
//progress bar colour
::ng-deep .mat-progress-bar-fill::after {
  background-color: $theme-green;
}
::ng-deep .mat-progress-bar-buffer {
  background: #e9ecef;
}
// product detail page mat tab
.vertical-tab-container {
  ::ng-deep .mat-tab-label {
    padding: 20px !important; // adjust this to whatever padding you need
  };
  .mat-tab-label {
    justify-content: start !important;
  }
   mat-ink-bar {
     display: none !important;
   }
}



// mat toggel (settings page)
mat-button-toggle-group {
  border-radius: 4px !important;
  font-style: normal;
  font-weight: 600;
  font-size: 14px;
  line-height: 21px;
}

mat-button-toggle {
  width: 164px;
  box-sizing: border-box;
}

// loading spinner
.loading-spinner {
  height: 200px;
}
.data-loading-spinner {
  height: 500px;
}
// no data message for all tables
.no-data {
  height: 200px;
  color: gray;
  display: flex;
  justify-content: center;
  align-items: center;
}
.move_to_frmField {
  ::ng-deep.mat-form-field-wrapper {
    padding-bottom: 0 !important;
  }
  ::ng-deep.mat-form-field-infix {
    padding: 0em 0 !important;
    border-top: 0em solid transparent !important;
  }
  .mat-select {
    width: 70%;
    padding: 6%;
    border-radius: 5px;
    border: 1px solid #adbace;
    margin-top: 6px;

    ::ng-deep.mat-select-placeholder {
      color: #646464 !important;
      font-weight: 400;
    }
    ::ng-deep.mat-select-arrow {
      color: #646464 !important;
    }
  }

  ::ng-deep.cdk-overlay-container,
  .cdk-global-overlay-wrapper {
    top: 40px !important;
  }
}
.disable-link {
  pointer-events: none;
}
.list-content-tooltip {
  white-space: pre-line;
}
// mat datetime picker
.mat-datetime-picker {
  .mat-form-field-suffix {
    top: 8px;
    .mat-datepicker-toggle {
      font-size: 20px;
    }
  }
}
