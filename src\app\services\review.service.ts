import { Injectable } from '@angular/core';
import { HttpHeaders, HttpClient, HttpParams } from '@angular/common/http';
import { Globals } from '../_globals/endpoints.global';
import { Observable, throwError } from 'rxjs';
import { map, catchError } from 'rxjs/operators';

@Injectable({
  providedIn: 'root',
})
export class ReviewService {
  private httpOptions: HttpHeaders;
  // private endpoints: any = ENDPOINTS;
  constructor(private globals: Globals, private http: HttpClient) {
    this.httpOptions = new HttpHeaders({
      'Content-Type': 'application/json',
    });
  }

  /**
   * get review filters
   */
  getReviewFilterList = (subscription_id, boolean): Observable<any> => {
    const ReviewFilterListEndpoint = this.globals.urlJoin(
      'review',
      'categoryFilter'
    );
    const options = {
      params: new HttpParams().set('review_page', boolean),
    };
    return this.http
      .get(
        ReviewFilterListEndpoint + subscription_id + '/review/filters',
        options
      )
      .pipe(
        map((response: any) => {
          return response;
        }),
        catchError((error) => throwError(error))
      );
  };

  /**
   * get review row index
   */
  getRowIndex = (subscription_id, row_index): Observable<any> => {
    const RowIndexEndpoint = this.globals.urlJoin('review', 'rowIndex');
    return this.http
      .get(
        RowIndexEndpoint + subscription_id + '/review/row_index/' + row_index
      )
      .pipe(
        map((response: any) => {
          return response;
        }),
        catchError((error) => throwError(error))
      );
  };

  /**
   * accept all suggestion
   */
  addLabel = (subscription_id, row_id, is_accepted): Observable<any> => {
    const LabelEndpoint = this.globals.urlJoin('home', 'acceptAllSuggestion');
    return this.http
      .patch(
        LabelEndpoint +
          subscription_id +
          '/batches/' +
          row_id +
          '/suggestions/status',
        {
          is_accepted: is_accepted,
        }
      )
      .pipe(
        map((response: any) => {
          return response;
        }),
        catchError((error) => throwError(error))
      );
  };

  /**
   * get product under review's detail
   * @param search
   * @param filter
   * @param start_date
   * @param end_date
   * @param subscription_id
   * @returns
   */
  getReviewProductDetail = (
    search,
    filter,
    start_date,
    end_date,
    subscription_id
  ): Observable<any> => {
    const ReviewFilterListEndpoint = this.globals.urlJoinWithParam(
      'review',
      'reviewProductDetail',
      subscription_id
    );
    const options = {
      params: new HttpParams()
        // .set('q', search)
        // .set('filter', filter)
        .set('start_date', start_date)
        .set('end_date', end_date),
    };
    search.forEach((item) => {
      options.params = options.params.append('q', item);
    });
    filter.forEach((item) => {
      options.params = options.params.append('filter', item);
    });
    return this.http.get(ReviewFilterListEndpoint, options).pipe(
      map((response: any) => {
        return response;
      }),
      catchError((error) => throwError(error))
    );
  };

  getReviewModeProductDetail = (
    search,
    filter,
    start_date,
    end_date,
    subscription_id,
    row_id,
    bucket
  ): Observable<any> => {
    const ReviewFilterListEndpoint = this.globals.urlJoin(
      'review',
      'reviewModeProductDetail'
    );
    const options = {
      params: new HttpParams()
        .set('bucket', bucket)
        .set('start_date', start_date)
        .set('end_date', end_date),
    };
    if (row_id != null) {
      options.params = options.params.append('row_id', row_id);
    }
    search.forEach((item) => {
      options.params = options.params.append('q', item);
    });
    filter.forEach((item) => {
      options.params = options.params.append('filter', item);
    });
    return this.http
      .get(ReviewFilterListEndpoint + subscription_id + '/review_mode', options)
      .pipe(
        map((response: any) => {
          return response;
        }),
        catchError((error) => throwError(error))
      );
  };

  /**
   * suggestion Accept/Decline
   */
  suggestionAccceptDecilne = (
    subs_id,
    row_id,
    prediction_id,
    suggestionObj,
    is_accepted
  ) => {
    const suggestionEndpoint = this.globals.urlJoin('review', 'suggestion');
    return this.http
      .patch(
        '/api/subscriptions/' +
          subs_id +
          '/inputs/' +
          row_id +
          '/predictions/' +
          prediction_id +
          '/suggestion' +
          '/status',
        {
          suggestion: suggestionObj,
          is_accepted: is_accepted,
        }
      )
      .pipe(
        map((response: any) => {
          return response;
        }),
        catchError((error) => throwError(error))
      );
  };

  /**
   * update suggestion (row)
   */
  updateSuggestions = (subs_id, row_id) => {
    const suggestionEndpoint = this.globals.urlJoin('review', 'suggestion');
    return this.http
      .get('/api/subscriptions/' + subs_id + '/inputs/' + row_id)
      .pipe(
        map((response: any) => {
          return response;
        }),
        catchError((error) => throwError(error))
      );
  };

  /**
   * Accept all/Decline all suggestions
   * @param subs_id
   * @param row_id
   * @param is_accepted
   * @returns
   */
  suggestionAccceptDecilneAll = (subs_id, row_id, is_accepted) => {
    const suggestionAllEndpoint = this.globals.urlJoin(
      'review',
      'UpdateAllsuggestion'
    );
    return this.http
      .patch(
        '/api/subscriptions/' +
          subs_id +
          '/inputs/' +
          row_id +
          '/suggestions' +
          '/status',
        {
          is_accepted: is_accepted,
        }
      )
      .pipe(
        map((response: any) => {
          return response;
        }),
        catchError((error) => throwError(error))
      );
  };

  /**
   * get particular row updated data
   * @param search
   * @param filter
   * @param start_date
   * @param end_date
   * @param subs_id
   * @param row_id
   * @returns
   */
  getReviewRow = (
    search,
    filter,
    start_date,
    end_date,
    subs_id,
    row_id
  ): Observable<any> => {
    const options = {
      params: new HttpParams()
        .set('start_date', start_date)
        .set('end_date', end_date),
    };
    search.forEach((item) => {
      options.params = options.params.append('q', item);
    });
    filter.forEach((item) => {
      options.params = options.params.append('filter', item);
    });
    const ReviewRowEndpoint = this.globals.urlJoin('review', 'reviewRow');
    return this.http
      .get('/api/subscriptions/' + subs_id + '/review/' + row_id, options)
      .pipe(
        map((response: any) => {
          return response;
        }),
        catchError((error) => throwError(error))
      );
  };

  /**
   * move row to specific bucket
   * @param batchId
   * @param obj
   * @param module_slug
   * @returns
   */
  bucketUpdate = (rowId, obj, subs_id) => {
    const buckUpdateEndpoint = this.globals.urlJoin('products', 'updateBatch');
    return this.http
      .patch(
        '/api/subscriptions/' + subs_id + '/inputs/' + rowId + '/bucket_update',
        obj,
        {
          headers: this.httpOptions,
          reportProgress: true,
          observe: 'events',
        }
      )
      .pipe(
        map((response: any) => {
          return response;
        }),
        catchError((error) => throwError(error))
      );
  };
}
