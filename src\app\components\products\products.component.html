<div class="wrapper" fxFlex="100" fxLayout="column">
  <div class="filter-container" fxLayout="column" fxLayoutAlign="center start">
    <div class="filter-head" fxLayoutAlign="space-between start">
      <div fxLayout="row" fxLayoutGap="10px" class="search-container">
        <mat-chip-list #chipList> </mat-chip-list>
        <mat-form-field
          fxFlex="70"
          fxFlex.gt-md="90"
          appearance="none"
          class="search-filter"
        >
          <input
            id="search"
            matInput
            placeholder="Search SKU, Product Name, Batch ID"
            [matChipInputFor]="chipList"
            [matChipInputSeparatorKeyCodes]="separatorKeysCodes"
            (matChipInputTokenEnd)="addQuery($event.value)"
            [(ngModel)]="search"
            #searchChips
          />
          <mat-icon matPrefix class="search-icon" (click)="addQuery(search)"
            >search</mat-icon
          >
        </mat-form-field>

        <div class="btn-container" fxLayout="row" fxLayoutGap="10px">
          <mat-form-field appearance="none" class="date-range-filter">
            <mat-select
              [(value)]="selected"
              (selectionChange)="getDataForDate($event.value)"
            >
              <div *ngFor="let option of datePickerOptions; trackBy: trackByDateOption">
                <!-- Recent -->
                <mat-option *ngIf="option.value === 'recent'" value="recent">
                  <div
                    fxLayout="column"
                    class="range-category"
                    *ngIf="option.value === 'recent'"
                    fxLayout="row"
                  >
                    <span> Recent</span> &nbsp;
                    <span class="date-range"> </span>
                  </div>
                </mat-option>
                <!-- Last Week / Month / Quarter -->
                <mat-option
                  [value]="option.value"
                  *ngIf="
                    option.value !== 'recent' && option.value !== 'custom_range'
                  "
                >
                  <div fxLayout="column" class="range-category">
                    {{ option.display }}
                    <span class="date-range">
                      {{ option.start_date | date : "mediumDate" }} -
                      {{ currentDate | date : "mediumDate" }}</span
                    >
                  </div>
                </mat-option>
                <!-- Custom range  -->
                <mat-option
                  *ngIf="option.value === 'custom_range'"
                  value="custom_range"
                  (click)="picker.open()"
                >
                  <div
                    fxLayout="row"
                    class="range-category"
                    fxLayoutAlign="start center"
                  >
                    <span>Custom Range</span>
                    <span
                      class="date-range"
                      *ngIf="customStartDate && customEndDate"
                    >
                      {{ customStartDate | date : "mediumDate" }} -
                      {{ customEndDate | date : "mediumDate" }}</span
                    >
                    <span fxLayout style="margin: 0 0 0 8px">
                      <mat-date-range-input
                        [rangePicker]="picker"
                        [min]="minDate"
                        [max]="maxDate"
                        style="display: none"
                      >
                        <input
                          matStartDate
                          #dateRangeStart
                          [(ngModel)]="customStartDate"
                        />
                        &nbsp;
                        <input
                          matEndDate
                          #dateRangeEnd
                          [(ngModel)]="customEndDate"
                          (dateChange)="
                            dateRangeChange(dateRangeStart, dateRangeEnd)
                          "
                        />
                      </mat-date-range-input>
                      <!-- date picker -->
                      <mat-datepicker-toggle
                        matPrefix
                        [for]="picker"
                      ></mat-datepicker-toggle>
                      <mat-date-range-picker
                        class="custom-date-icon"
                        #picker
                      ></mat-date-range-picker>
                    </span>
                  </div>
                </mat-option>
              </div>
            </mat-select>
            <div class="date-range-icon">
              <img src="assets/images/calender.svg" />
            </div>
          </mat-form-field>
        </div>

        <!-- product filters -->
        <mat-form-field
          appearance="none"
          class="product-filter"
          fxFlex="20"
          *ngFor="let item of productFilterList; let i = index; trackBy: trackByFilterValue"
        >
          <mat-select
            placeholder="{{ item.display_name }}"
            [(ngModel)]="selectedProducts[i]"
            (ngModelChange)="getProductSelection()"
            name="selectedProducts"
            multiple
          >
            <mat-option
              #mulVal
              *ngFor="let mulVals of item.values; trackBy: trackByOptionValue"
              [value]="mulVals"
            >
              {{ mulVals }}
            </mat-option>
          </mat-select>
        </mat-form-field>

        <!-- reset button -->
        <button
          mat-button
          fxLayout
          fxLayoutAlign="center center"
          class="reset-btn filled-btn-primary"
          matTooltip="Clear search and filters"
          matTooltipPosition="above"
          (click)="resetFilters()"
        >
          Reset
        </button>
        <div
          class="view-messages-icon"
          matTooltip="View comments"
          matTooltipPosition="above"
          [routerLink]="'/comments'"
          [queryParams]="{ sub: subscriptionId, origin: '/products' }"
        >
          <!-- <div class="dot"></div> -->
          <img src="assets/images/message.svg" />
        </div>
      </div>
    </div>
    <!-- mat chip for search attr -->
    <div>
      <mat-chip
        class="search-chip"
        *ngFor="let item of filterList; trackBy: trackByFilterValue"
        [ngStyle]="{
          'background-image': item.is_batch
            ? 'linear-gradient(to right, #c5d9f3 ' +
              item.progress +
              '%, white ' +
              item.progress +
              '%)'
            : 'none'
        }"
        [selectable]="selectable"
        [removable]="removable"
        (removed)="removeQuery(item)"
        [matTooltip]="item.is_batch ? item.progress + '%' : null"
        [matTooltipPosition]="'above'"
      >
        {{ item.value }}
        <mat-icon matChipRemove *ngIf="removable">cancel</mat-icon>
      </mat-chip>
    </div>
  </div>

  <div class="table-wrapper" fxFlex="100" fxLayout="column" fxLayoutGap="20px">
    <mat-tab-group
      (selectedTabChange)="bucketTabChange($event)"
      [selectedIndex]="selectedIndex"
    >
      <mat-tab [label]="tab.value" *ngFor="let tab of tabs; trackBy: trackByTabSlug">
        <ng-template mat-tab-label>
          <span matTooltip="{{ tab.description }}" matTooltipPosition="above"
            >{{ tab.name }} </span
          >&nbsp;
          <span
            class="batch-count"
            fxLayoutAlign="center center"
            *ngIf="bucketWiseCount"
          >
            {{ bucketWiseCount[tab.slug] }}</span
          >
        </ng-template>

        <div class="mat-elevation-z8 table-section" style="margin-top: 12px">
          <!-- Loading indicator for tab switching -->
          <div
            class="loading-spinner"
            fxLayoutAlign="center center"
            *ngIf="tableDataLoading"
            style="min-height: 200px;"
          >
            <mat-spinner
              fxLayoutAlign="center center"
              diameter="60"
              strokeWidth="3"
            ></mat-spinner>
          </div>

          <!-- Table content - hidden when loading -->
          <div *ngIf="!tableDataLoading">
            <!-- bulk edit and move rows header section  -->

            <div
              class="selectionBoxContainer"
              *ngIf="
                selectedSKUList.length > 1 ||
                (productTableData.length == 1 && selectedSKUList.length == 1)
              "
            >
            <!-- in this Div we are showing selected rows count -->

            <div
              class="selectionBox"
              fxLayout="row"
              fxLayoutAlign="start center"
            >
              <div
                fxLayout="row"
                fxLayoutAlign="start center"
                class="text-container"
              >
                <span *ngIf="selectedSKUList.length == productTableData.length"
                  >All&nbsp;</span
                >
                <span *ngIf="selectedSKUList.length == 1">
                  <span class="skuLengthCount">{{
                    selectedSKUList.length
                  }}</span>
                  row&nbsp;
                </span>
                <span *ngIf="selectedSKUList.length > 1">
                  <span class="skuLengthCount">{{
                    selectedSKUList.length
                  }}</span>
                  rows&nbsp;
                </span>
                <span>in this page&nbsp;</span>
                <span
                  *ngIf="
                    (selectedSKUList.length == productTableData.length &&
                      selectedSKUList.length == 1) ||
                    selectedSKUList.length > 1
                  "
                >
                  are&nbsp;
                </span>
                selected&nbsp;
                <span *ngIf="!selectedAll">
                  or
                  <span class="action-text" (click)="selectAllItems('all')"
                    >Select All</span
                  >
                </span>
                <span *ngIf="selectedAll">
                  or
                  <span class="action-text" (click)="selectAllItems('')"
                    >Deselect All</span
                  >
                </span>
              </div>
              <div class="separator"></div>

              <!-- This div ccontains bulk edit in leaf-node  -->

              <div
                class="leaf-node-container"
                *ngIf="tabs[selectedIndex]?.value !== 'IN_PROGRESS'"
                (click)="$event.stopPropagation()"
              >
                <span class="skuLengthCount">Leaf Node:</span>
                <div fxLayout="column" class="leaf-node-column">
                  <!-- Edit Mode -->
                  <div
                    fxLayout="row"
                    fxLayoutAlign="space-between center"
                    class="attribute-search-dropdown"
                  >
                    <div
                      fxLayout="row"
                      fxLayoutAlign="space-between center"
                      class="view-mode"
                    >
                      <span
                        matTooltipPosition="above"
                        matTooltipClass="list-content-tooltip"
                        class="selection-name"
                      >
                        {{ globalSelectionName }}
                      </span>

                      <span
                        class="edit-btn-row"
                        [class.disabled]="currentOpenDropdown"
                        [matTooltip]="currentOpenDropdown ? 'Close row edit first' : (isBulkEditOpen ? 'Close' : 'Edit')"
                        matTooltipPosition="above"
                        (click)="!currentOpenDropdown && toggleBulkDropdown()"
                      >
                      <ng-container *ngIf="isBulkEditOpen; else editIcon">
                        <!-- Close SVG -->
                        <svg
                          width="20"
                          height="20"
                          viewBox="0 0 20 20"
                          fill="none"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M4 4L16 16"
                            stroke="#181B23"
                            stroke-width="1.5"
                            stroke-linecap="round"
                          />
                          <path
                            d="M16 4L4 16"
                            stroke="#181B23"
                            stroke-width="1.5"
                            stroke-linecap="round"
                          />
                        </svg>
                      </ng-container>
                      <ng-template #editIcon>
                        <!-- Edit (Pencil) SVG -->
                        <svg
                          width="20"
                          height="20"
                          viewBox="0 0 20 20"
                          fill="none"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M3.33334 18.3333H16.6667"
                            stroke="#181B23"
                            stroke-width="1.5"
                            stroke-linecap="round"
                          />
                          <path
                            d="M11.5733 3.05258C11.5733 3.05258 11.6505 4.36566 12.8091 5.52426C13.9677 6.68286 15.2808 6.7601 15.2808 6.7601M4.79851 14.7856L3.54777 13.5349"
                            stroke="#181B23"
                            stroke-width="1.5"
                          />
                          <path
                            d="M12.1913 2.43451L11.5734 3.05243L5.89255 8.73325C5.50778 9.11803 5.31539 9.31042 5.14993 9.52255C4.95476 9.77278 4.78743 10.0435 4.6509 10.33C4.53516 10.5729 4.44912 10.831 4.27704 11.3472L3.54787 13.5347L3.36963 14.0694C3.28495 14.3235 3.35107 14.6036 3.54042 14.7929C3.72977 14.9823 4.00986 15.0484 4.2639 14.9637L4.79862 14.7855L6.98612 14.0563L6.98615 14.0563C7.50236 13.8842 7.76047 13.7982 8.00332 13.6824C8.2898 13.5459 8.56055 13.3786 8.81078 13.1834C9.0229 13.0179 9.21529 12.8256 9.60006 12.4408L9.60007 12.4408L15.2809 6.75995L15.8988 6.14203C16.9226 5.11822 16.9226 3.45831 15.8988 2.43451C14.875 1.41071 13.2151 1.41071 12.1913 2.43451Z"
                            stroke="#181B23"
                            stroke-width="1.5"
                          />
                        </svg>
                      </ng-template>
                      </span>
                    </div>

                    <div class="menu-container">
                      <div
                        *ngIf="isBulkEditOpen"
                        class="dropdown-content-bulk"
                        (clickOutside)="handleBulkClickOutside()"
                      >
                        <div class="search-header">

                          <div class="search-option">
                            <div class="search-container">
                              <mat-icon class="search-icon">search</mat-icon>
                              <input
                                matInput
                                placeholder="Search..."
                                [(ngModel)]="searchTerm"
                                (input)="
                                  filterMyOptions($event, 'bulkinput');
                                  $event.stopPropagation()
                                "
                                class="search-input"
                                (click)="$event.stopPropagation()"
                              />
                            </div>
                          </div>
                        </div>
                        <div class="options-container">
                          <!-- Loading State -->
                          <mat-option *ngIf="isLoadingForOptions" disabled class="loading-option">
                            <div class="loading-container">
                              <mat-progress-spinner mode="indeterminate" diameter="20" color="primary">
                              </mat-progress-spinner>
                              <span>Searching...</span>
                            </div>
                          </mat-option>

                          <!-- No Results -->
                          <mat-option
                            *ngIf="!isLoadingForOptions && bulkAttributeOptions?.length === 0"
                            disabled
                            class="message-option"
                          >
                            <div class="message-container">
                              <mat-icon>info</mat-icon>
                              <span>No matching results found</span>
                            </div>
                          </mat-option>

                          <!-- Results List -->
                          <div class="scrollable-results">
                            <mat-option
                              *ngFor="let option of bulkAttributeOptions; trackBy: trackByOptionValue"
                              class="search-result-item"
                              [title]="option"
                              globalSelectionName = option;
                              (click)="handleBulkSelection(option)"
                            >
                              {{ option?.substring(0, 25) }}
                            </mat-option>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div
                *ngIf="tabs[selectedIndex]?.value !== 'IN_PROGRESS'"
                class="separator"
              ></div>

              <!-- this div contains bulk move -->

              <form [formGroup]="bulkMoveToForm">
                <mat-form-field appearance="none" class="move_to_frmField">
                  <mat-select placeholder="Move To" formControlName="moveTo">
                    <mat-option
                      *ngFor="let bucket of permissionsObject[tab.slug]; trackBy: trackByOptionValue"
                      [value]="bucket.value"
                      (click)="
                        bulkUpdateBucket(
                          productTableData,
                          selectedBucketSlug,
                          bucket.value
                        )
                      "
                    >
                      <span class="leafNodeEditName">{{
                        bucket.display_name
                      }}</span>
                    </mat-option>
                  </mat-select>
                </mat-form-field>
              </form>
            </div>
          </div>

          <!-- table -->
          <table
            mat-table
            [dataSource]="dataSource"
            *ngIf="productTableData && !tableDataLoading"
          >
            <ng-container
              matColumnDef="{{ item.slug }}"
              *ngFor="let item of productHeaderData; trackBy: trackByHeaderSlug"
            >
              <ng-container
                style="padding: 10px"
                *ngIf="item?.slug === 'selection'"
              >
                <th mat-header-cell *matHeaderCellDef>
                  <mat-checkbox

                    [(ngModel)]="selectedAll"
                    (change)="selectAllItems($event)"
                  >
                  </mat-checkbox>
                </th>
              </ng-container>

              <div *ngIf="item.slug != 'actions'">
                <th mat-header-cell *matHeaderCellDef>
                  {{ item.display_name | underscoreAsSpace }}
                </th>
              </div>
              <div *ngIf="item.slug === 'actions'" fxFlex="10">
                <th mat-header-cell *matHeaderCellDef>
                  <span *ngIf="permissionsObject[tab.slug]?.length > 0">
                    Actions
                  </span>
                </th>
              </div>
              <td
                mat-cell
                *matCellDef="let element; index as i"
                style="padding-right: 10px"
              >
                <!-- row id column -->
                <div fxLayout="column" *ngIf="item.slug == 'selection'">
                  <mat-checkbox
                    [checked]="isRowIdSelected(element.row_id)"
                    (change)="selectSKU($event, element)"
                  >
                  </mat-checkbox>
                </div>
                <div fxLayout="column" *ngIf="item.slug == 'row_id'">
                  <span fxLayout="row" class="text-theme-primary">
                    {{ element["row_id"] }}
                  </span>
                  <p class="batch-chip chip">{{ element["batch_id"] }}</p>
                  <p fxLayout="row" class="batch-date">
                    {{ element["created_at"] | date : "longDate" }}
                  </p>
                </div>
                <!-- product description -->
                <div
                  fxLayout="column"
                  *ngIf="item.slug == 'product_description'"
                >
                  <span
                    fxLayout="row"
                    style="word-break: break-all"
                    matTooltip="{{ element['product_description'].value }}"
                  >
                    {{ element["product_description"].value | truncate : 80 }}
                  </span>
                </div>
                <!-- category -->
                <div fxLayout="column" *ngIf="item.slug == 'category'">
                  <span
                    fxLayout="row"
                    matTooltip="{{ element['category'].value }}"
                  >
                    {{ element["category"].value | truncate : 10 }}
                  </span>
                </div>
                <!-- other columns -->
                <div
                  fxLayout="column"
                  class="leafnodeContaner"
                  *ngIf="
                    item.slug != 'row_id' &&
                    item.slug != 'actions' &&
                    item.slug != 'product_description' &&
                    item.slug != 'category' &&
                    element[item.slug] != null
                  "
                  [matTooltip]="
                    element[item.slug]?.length > 20 ? element[item.slug] : null
                  "
                >
                  <div
                    fxLayout="row"
                    fxLayoutAlign="space-between center"
                    class="attribute-search-dropdown"
                  >
                    <div
                      fxLayout="row"
                      fxLayoutAlign="space-between center"
                      class="view-mode"
                    >
                    <p
                    matTooltip="{{ element[item.slug].value }}"
                    matTooltipPosition="above"
                  >
                        {{
                          element[item.slug].value ||
                            getSelectedValue(element.row_id, item.slug)
                        }}
                      </p>
                      <span
                      class="edit-btn-row"
                        *ngIf="item.is_editable && bucket != 'IN_PROGRESS'"
                        [matTooltip]="isDropdownOpen(element.row_id, item.slug) ? 'Close' : (isBulkEditOpen ? 'Close bulk edit first' : 'Edit')"
                        matTooltipPosition="above"
                        [class.disabled]="isBulkEditOpen"
                        (click)="!isBulkEditOpen && toggleDropdown(element.row_id, item.slug, element, $event)"
                      >
                      <ng-container *ngIf="isDropdownOpen(element.row_id, item.slug); else rowEditIcon">

                        <svg
                          width="20"
                          height="20"
                          viewBox="0 0 20 20"
                          fill="none"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M4 4L16 16"
                            stroke="#181B23"
                            stroke-width="1.5"
                            stroke-linecap="round"
                          />
                          <path
                            d="M16 4L4 16"
                            stroke="#181B23"
                            stroke-width="1.5"
                            stroke-linecap="round"
                          />
                        </svg>
                      </ng-container>
                      <ng-template #rowEditIcon>

                        <svg
                          width="20"
                          height="20"
                          viewBox="0 0 20 20"
                          fill="none"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M3.33334 18.3333H16.6667"
                            stroke="#181B23"
                            stroke-width="1.5"
                            stroke-linecap="round"
                          />
                          <path
                            d="M11.5733 3.05258C11.5733 3.05258 11.6505 4.36566 12.8091 5.52426C13.9677 6.68286 15.2808 6.7601 15.2808 6.7601M4.79851 14.7856L3.54777 13.5349"
                            stroke="#181B23"
                            stroke-width="1.5"
                          />
                          <path
                            d="M12.1913 2.43451L11.5734 3.05243L5.89255 8.73325C5.50778 9.11803 5.31539 9.31042 5.14993 9.52255C4.95476 9.77278 4.78743 10.0435 4.6509 10.33C4.53516 10.5729 4.44912 10.831 4.27704 11.3472L3.54787 13.5347L3.36963 14.0694C3.28495 14.3235 3.35107 14.6036 3.54042 14.7929C3.72977 14.9823 4.00986 15.0484 4.2639 14.9637L4.79862 14.7855L6.98612 14.0563L6.98615 14.0563C7.50236 13.8842 7.76047 13.7982 8.00332 13.6824C8.2898 13.5459 8.56055 13.3786 8.81078 13.1834C9.0229 13.0179 9.21529 12.8256 9.60006 12.4408L9.60007 12.4408L15.2809 6.75995L15.8988 6.14203C16.9226 5.11822 16.9226 3.45831 15.8988 2.43451C14.875 1.41071 13.2151 1.41071 12.1913 2.43451Z"
                            stroke="#181B23"
                            stroke-width="1.5"
                          />
                        </svg>
                      </ng-template>
                      </span>
                    </div>
                    <div class="menu-container">
                      <div
                        *ngIf="isDropdownOpen(element.row_id, item.slug)"
                        class="dropdown-content"
                        (clickOutside)="handleClickOutside(element.row_id, item.slug, element)"
                      >
                        <div class="search-header">

                          <div class="search-option">
                            <div class="search-container">
                              <mat-icon class="search-icon">search</mat-icon>
                              <input
                                matInput
                                placeholder="Search..."
                                [ngModel]="getSearchTermForCell(element.row_id, item.slug)"
                                (ngModelChange)="updateSearchTerm($event, element.row_id, item.slug)"
                                (input)="filterMyOptions($event, 'rowinput'); $event.stopPropagation()"
                                (click)="$event.stopPropagation()"
                                class="search-input"
                              />
                            </div>
                          </div>

                        </div>
                        <div class="options-container">
                          <!-- Loading State -->
                          <mat-option
                          *ngIf="isLoadingForOptions"
                          disabled
                          class="loading-option"
                        >
                          <div class="loading-container">
                            <mat-progress-spinner
                              mode="indeterminate"
                              diameter="20"
                              color="primary"
                            ></mat-progress-spinner>
                            <span>Searching...</span>
                          </div>
                        </mat-option>

                          <!-- No Results -->
                          <mat-option
                          *ngIf="
                            !isLoadingForOptions &&
                            attributeOptions?.length === 0
                          "
                          disabled
                          class="message-option"
                        >
                          <div class="message-container">
                            <mat-icon>info</mat-icon>
                            <span>No matching results..</span>
                          </div>
                        </mat-option>

                          <!-- Error State -->
                          <mat-option
                          *ngIf="searchError"
                          disabled
                          class="message-option error"
                        >
                          <div class="message-container">
                            <mat-icon>info</mat-icon>
                            <span>No matching results..</span>
                          </div>
                        </mat-option>

                          <!-- Options List -->
                          <div class="scrollable-results">
                          <mat-option
                            *ngFor="let option of attributeOptions; trackBy: trackByOptionValue"
                            class="search-result-item"
                            [title]="option"
                            (click)="updateSelection(item.slug, option, element, element[item.slug]?.value)"
                          >
                            {{ option?.substring(0, 25) }}
                          </mat-option>
                        </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <!-- actions column -->
                <div
                  *ngIf="
                    item.slug === 'actions' &&
                    permissionsObject[tab.slug]?.length > 0
                  "
                >
                  <form [formGroup]="moveToForm">
                    <mat-form-field appearance="none" class="move_to_frmField">
                      <mat-select placeholder="Move" formControlName="moveTo">
                        <mat-option
                          *ngFor="let bucket of permissionsObject[tab.slug]; trackBy: trackByOptionValue"
                          [value]="bucket.value"
                          (click)="
                            updateBucket(
                              element['row_id'],
                              element['bucket'],
                              bucket.value
                            )
                          "
                        >
                          {{ bucket.display_name }}
                        </mat-option>
                      </mat-select>
                    </mat-form-field>
                  </form>
                </div>
                <div
                  fxLayout="column"
                  *ngIf="item.slug == 'comments'"
                  style="width: 40px"
                >
                  <div class="comments-icon">
                    <div
                      class="dot-comments"
                      *ngIf="element.has_comments"
                    ></div>
                    <img
                      [routerLink]="['/comments']"
                      [queryParams]="{
                        origin: '/products',
                        row_id: element['row_id'],
                        sub: subscriptionId
                      }"
                      src="assets/images/message.svg"
                    />
                  </div>
                </div>
              </td>
            </ng-container>

            <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
            <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
          </table>
          <!-- progress spinner -->
          <div
            class="loading-spinner"
            fxLayoutAlign="center center"
            *ngIf="tableDataLoading"
          >
            <mat-spinner
              fxLayoutAlign="center center"
              diameter="60"
              strokeWidth="3"
            ></mat-spinner>
          </div>
          <!-- no data section -->
          <div
            class="no-data"
            *ngIf="!tableDataLoading && productTableData?.length == 0"
            fxLayout="row"
            fxLayoutGap="10px"
          >
            <mat-icon fontSet="material-icons-outlined">info</mat-icon>
            <span>Nothing to display.</span>
          </div>
          <mat-paginator
            [length]="totalItems"
            [pageSize]="size"
            [pageIndex]="page - 1"
            *ngIf="productTableData?.length > 0"
            [pageSizeOptions]="[10, 20, 50, 100]"
            (page)="onPaginateChange($event)"
            showFirstLastButtons
          >
          </mat-paginator>
          </div> <!-- Close table content div -->
        </div>
      </mat-tab>
    </mat-tab-group>
  </div>
</div>

