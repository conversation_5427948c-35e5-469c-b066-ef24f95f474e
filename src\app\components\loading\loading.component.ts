import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Params, Router } from '@angular/router';
import { Auth0Service } from 'src/app/services/auth0.service';
import { AuthService } from '@auth0/auth0-angular';

@Component({
  selector: 'app-loading',
  templateUrl: './loading.component.html',
  styleUrls: ['./loading.component.scss'],
})
export class LoadingComponent implements OnInit {
  token;
  queryParam;
  subscriptionId;

  constructor(
    private activatedRoute: ActivatedRoute,
    private auth0: Auth0Service,
    private router: Router,
    private auth: AuthService
  ) {}

  ngOnInit(): void {
    this.subscriptionId = localStorage.getItem('SubscriptionID');
    this.activatedRoute.queryParams.subscribe((params: Params) => {
      this.queryParam = params;
      console.log(this.queryParam);
      // get the came from cookie and store in localhost
      // const cameFromCookie = document.cookie
      //   .split(';')
      //   .filter((el) => el.startsWith('came_from='))[0]
      //   .split('came_from=')[1];
    });
  }
}
