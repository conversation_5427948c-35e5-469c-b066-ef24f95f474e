<!-- side navigation -->
<div class="sidebar">
  <div class="sidebar__nav" fxLayout="column">
    <ul fxLayout="column" fxLayoutGap="5px">
      <!-- nav list -->
      <li matRipple>
        <a
          fxLayout="column"
          fxLayoutAlign="space-between center"
          [routerLink]="['/home']" [queryParams]="{ sub: subscriptionId }" matTooltip="View uploaded batches and monitor their progress" matTooltipPosition="right"
        >
          <div
            class="menu-wrapper"
            fxLayout="column"
            fxLayoutAlign="space-between center"
          >
            <div
              class="menu-title"
              fxLayoutAlign="center center"
              routerLinkActive="active"
              #home="routerLinkActive"
            >
              <img
                [src]="
                home.isActive
                    ? 'assets/images/side-nav-svg/home-active.svg'
                    : 'assets/images/side-nav-svg/home.svg'
                "
              />
            </div>
            <span class="menu-text" routerLinkActive="active-text">Home</span>
          </div>
        </a>
      </li>
      <li>
        <a
          fxLayout="column"
          fxLayoutAlign="space-between center"
          [routerLink]="['/products']" [queryParams]="{ sub: subscriptionId }" matTooltip="Monitor the classification processes at SKU level." matTooltipPosition="right"
        >
          <div
            class="menu-wrapper"
            fxLayout="column"
            fxLayoutAlign="space-between center"
          >
            <div
              class="menu-title"
              fxLayoutAlign="center center"
              routerLinkActive="active"
              #products="routerLinkActive"
            >
              <img
                [src]="
                products.isActive
                    ? 'assets/images/side-nav-svg/product-active.svg'
                    : 'assets/images/side-nav-svg/products.svg'
                "
              />
            </div>
            <span class="menu-text" routerLinkActive="active-text"
              >Products</span
            >
          </div>
        </a>
      </li>
    </ul>

    <!-- <div fxLayout="column" class="support-icon">
      <ul>
        <li>
          <a
            fxLayout="column"
            fxLayoutAlign="space-between center"
            [routerLink]="['/help']" [queryParams]="{ sub: subscriptionId }"
          >
            <div
              class="menu-wrapper"
              fxLayout="column"
              fxLayoutAlign="space-between center"
            >
              <div
                class="menu-title"
                fxLayoutAlign="center center"
                routerLinkActive="active"
                #help="routerLinkActive"
              >
                <img
                  [src]="
                  help.isActive
                      ? 'assets/images/side-nav-svg/help-white.svg'
                      : 'assets/images/side-nav-svg/help.svg'
                  "
                />
              </div>
              <p class="menu-text" routerLinkActive="active-text">Help</p>
            </div>
          </a>
        </li>
      </ul>
    </div> -->
  </div>
</div>
