import { Component, OnInit } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { ImageViewerDialogComponent } from '../../../_dialogs/image-viewer-dialog/image-viewer-dialog.component';
import { Router, ActivatedRoute, Params } from '@angular/router';
import { HttpErrorResponse } from '@angular/common/http';
import { ReviewService } from '../../../services/review.service';
import { MatSnackBar } from '@angular/material/snack-bar';
import { ProductDetailsService } from '../../../services/product-details.service';
import { FormControl } from '@angular/forms';
import { Observable } from 'rxjs';
import { UndoSnackbarComponent } from '../../../_dialogs/undo-snackbar/undo-snackbar.component';
import { ChoiceWithIndices } from '@flxng/mentions';
import { CommentsService } from '../../../services/comments.service';
import { UserService } from 'src/app/services/user.service';
import { ProductsService } from 'src/app/services/products.service';

@Component({
  selector: 'app-product-details',
  templateUrl: './product-details.component.html',
  styleUrls: ['./product-details.component.scss'],
})
export class ProductDetailsComponent implements OnInit {
  subscriptionId: any;
  row_id: any;
  bucket: any;
  search_queries;
  start_date;
  end_date;
  tableDataLoading: boolean = false;
  attributeLoading: boolean = false;
  tabList: any;
  myControl = new FormControl();
  autocompleteAttrSlug;
  optionsLoading: boolean;
  valArr: any = [];
  updatedVal: any = [];
  prediction_id: any;
  filteredOptions: Observable<any[]>;
  autoCompleteSuggestions: string[] = [];
  dataLoading: boolean = true;
  productImg: any;
  bucketVal;
  productBucket;
  productTitle;
  productDetailsData;
  productIds;
  previousPageUrl: any;
  filtersFromLocalStorage;
  nextRowId;
  previousRowId;
  product_list;
  permissionsObject;
  moveToBucketList: [];

  constructor(
    public dialog: MatDialog,
    private router: Router,
    private route: ActivatedRoute,
    private productdetailsService: ProductDetailsService,
    public matSnackbar: MatSnackBar,
    private commentsService: CommentsService,
    private userService: UserService,
    private productService: ProductsService
  ) {}

  ngOnInit() {
    this.tableDataLoading = true;
    this.route.queryParams.subscribe((params: Params) => {
      this.row_id = params.row_id;
      this.bucket = params.bucket;
      //   if(this.bucket == undefined){
      //     this.bucket = params.from.toUpperCase();
      // }
    });
    this.subscriptionId = localStorage.getItem('SubscriptionID');
    this.previousPageUrl = this.route.snapshot.queryParams['from'];
    // retrieve product page permissions
    // retrieve product page permissionsObject
    this.permissionsObject = this.userService.appPermissions.bucket_permissions;
    // get user data from local storage
    if (localStorage.getItem('user')) {
      this.userData = JSON.parse(localStorage.getItem('user'));
    }

    // apply filters based on origin
    if (this.previousPageUrl == 'products') {
      this.filtersFromLocalStorage = JSON.parse(
        localStorage.getItem('productsFilterObj')
      );
    } else if (this.previousPageUrl == 'review') {
      this.filtersFromLocalStorage = JSON.parse(
        localStorage.getItem('reviewFilterObj')
      );
    }
    if (this.filtersFromLocalStorage) {
      this.search_queries = this.filtersFromLocalStorage[this.subscriptionId].q;
      this.start_date =
        this.filtersFromLocalStorage[this.subscriptionId].start_date;
      this.end_date =
        this.filtersFromLocalStorage[this.subscriptionId].end_date;
      this.product_list =
        this.filtersFromLocalStorage[this.subscriptionId].product_list;
    }
    this.getProductDetailsData(this.row_id);
    this.getAttributeHeader();
    this.getUserNameList();
  }

  /**
   * get user list for tagging in comments
   */
  getUserNameList = () => {
    this.commentsService.getUserNamesToTag(this.subscriptionId).subscribe({
      next: (resp) => {
        this.mentionUsers = resp.result;
        // return resp;
      },
    });
  };

  /**
   * Image  slider
   * @param productImg
   */
  openImageSlider(productImg) {
    this.dialog.open(ImageViewerDialogComponent, {
      data: { productImg },
    });
  }
  productModeChecked: boolean = true;
  /**
   * Toggle All settings
   * @param e
   */
  toggleSwitches = (e) => {
    if (e.checked) {
      this.productModeChecked = true;
    } else {
      this.route.queryParams.subscribe((params: Params) => {
        this.row_id = params.row_id;
        this.bucket = params.bucket;
      });
      this.router.navigate(['/product-details/review-mode'], {
        queryParams: {
          sub: this.subscriptionId,
          row_id: this.row_id,
          bucket: this.bucket,
        },
      });
    }
  };

  rowId;
  predictionId;
  predictionName;

  /**
   * prediction details
   */
  getProductDetailsData = (row_id) => {
    this.productdetailsService
      .getProductDetails(
        this.subscriptionId,
        row_id,
        this.bucket,
        this.search_queries,
        this.product_list,
        this.start_date,
        this.end_date
      )
      .subscribe({
        next: (resp) => {
          this.productDetailsData = resp.result;
          this.productTitle = this.productDetailsData?.title;
          this.productImg = this.productDetailsData?.images;
          this.productBucket = this.productDetailsData?.bucket;
          this.productIds = this.productDetailsData?.ids;
          this.nextRowId = this.productDetailsData.next_row;
          this.previousRowId = this.productDetailsData.previous_row;
          this.bucketVal = this.productDetailsData?.bucket.toLowerCase();
          this.predictionId = resp.result[0]?.predictions[0].prediction_id;
          this.predictionName = resp.result[0]?.predictions[0].display_name;
          this.moveToBucketList = this.permissionsObject[this.bucketVal];
          this.rowId = row_id;
          this.getCommentThread(
            this.subscriptionId,
            this.threadPage,
            this.threadSize,
            this.commentsListForType,
            this.rowId,
            this.searchInComment,
            true
          );

          this.tableDataLoading = false;
        },
        error: (HttpResponse: HttpErrorResponse) => {
           this.snackbarService.openSnackBar(`${HttpResponse.error.detail}`, 'OK');
        },
      });
  };

  /**
   * To get side tab info
   * @param subscriptionId
   */
  getAttributeHeader = () => {
    this.productdetailsService.getAttributeList(this.subscriptionId).subscribe({
      next: (resp) => {
        this.tabList = resp.result;
        // this.tabList.forEach((data) => {
        //   this.tabKeys = data.slug;
        // });
      },
    });
  };

  update(event: KeyboardEvent) {
    event.preventDefault();
    this.updateFieldsOnClick();
  }
  /**
   * Updates  form field /drop down
   * @param predictionId
   * @param val
   */
  onChangeOfInput = (predictionId, val) => {
    this.valArr = [];
    this.valArr.push(val);
    this.updatedVal.push({
      prediction_id: predictionId,
      values: this.valArr,
    });
    this.valArr = [];
  };

  /**
   * update prediction
   */
  updateFieldsOnClick = () => {
    this.productdetailsService
      .patchEditedResponse(this.subscriptionId, this.row_id, this.updatedVal)
      .subscribe({
        next: (resp) => {
          if (resp.detail == 'success') {
            // undo snackbar
            this.matSnackbar.openFromComponent(UndoSnackbarComponent, {
              duration: 30000,
              // passing args as data
              data: {
                response: resp.detail,
                subscriptionId: this.subscriptionId,
                id: this.row_id,
                onUndo: () => {
                  this.getProductDetailsData(this.row_id);
                },
              },
            });
            this.getProductDetailsData(this.row_id);
            this.updatedVal = [];
            this.valArr = [];
          }
          this.updatedVal = [];
          this.valArr = [];
        },
        error: (HttpResponse: HttpErrorResponse) => {
          this.getProductDetailsData(this.row_id);
          this.updatedVal = [];
          this.valArr = [];
           this.snackbarService.openSnackBar(`${HttpResponse.error.detail}`, 'OK');
        },
      });
  };

  /**
   * go to previous product in queue
   * @param previousRowId
   */
  previousPage = (previousRowId) => {
    if (previousRowId === 'None') {
      this.matSnackbar.open('No more Products Available', 'OK', {
        duration: 5000,
      });
      return;
    } else {
      this.dataLoading = true;
      this.router.navigate([], {
        queryParams: {
          row_id: previousRowId,
          sub: this.subscriptionId,
          from: this.previousPageUrl,
          bucket: this.bucket,
        },
        preserveFragment: false,
        queryParamsHandling: '',
      });
      this.getAttributeHeader();
      this.getProductDetailsData(previousRowId);
      // this.getComments(previousRowId, this.module_slug);
    }
  };

  /**
   * go to next product in queue
   * @param nextRowId
   */
  nextPage = (nextRowId) => {
    // this.enableAddComment = false;
    if (nextRowId === 'None') {
      this.matSnackbar.open('No more Products Available', 'OK', {
        duration: 5000,
      });
      return;
    } else {
      this.dataLoading = true;
      this.router.navigate([], {
        queryParams: {
          row_id: nextRowId,
          sub: this.subscriptionId,
          from: this.previousPageUrl,
          bucket: this.bucket,
        },
        preserveFragment: false,
        queryParamsHandling: '',
      });
      this.getAttributeHeader();
      this.getProductDetailsData(nextRowId);
      // this.getComments(nextRowId, this.module_slug);
    }
  };

  commentThread: any[] = [];
  threadPage: number = 1;
  threadSize: number = 10;
  threadTotalItems: number;
  threadTotalPage: number;
  commentsLoading: boolean;
  commentsListForType = 'row';
  userData;
  searchInComment = '';
  /**
   * event fired when comment thread section is scrolled
   * fetching new pages
   */
  onCommentThreadScroll = () => {
    // check if comment thread length is equal to total item capacity of pages
    // or check if total item size > total item capacity of pages
    // increment pageSize by 1
    if (this.threadTotalItems > this.commentThread.length) {
      if (this.commentThread.length == this.threadSize * this.threadPage) {
        this.threadPage++;
        this.getCommentThread(
          this.subscriptionId,
          this.threadPage,
          this.threadSize,
          this.commentsListForType,
          this.rowId,
          this.searchInComment,
          true
        );
      } else {
        this.getCommentThread(
          this.subscriptionId,
          this.threadPage,
          this.threadSize,
          this.commentsListForType,
          this.rowId,
          this.searchInComment,
          true
        );
      }
    }
  };

  /**
   * get comment thread for respective sku/batch comment
   * @param subs_id
   * @param page
   * @param size
   * @param category
   * @param batch_or_row_id
   * @param q
   * @param comment_thread
   */
  getCommentThread = (
    subs_id,
    page,
    size,
    category,
    batch_or_row_id,
    q,
    comment_thread
  ) => {
    this.commentsLoading = true;
    this.commentsService
      .getCommentsList(
        subs_id,
        page,
        size,
        category,
        batch_or_row_id,
        q,
        comment_thread
      )
      .subscribe({
        next: (resp) => {
          this.commentsLoading = false;
          this.threadPage = resp.page;
          this.threadSize = resp.page_size;
          this.threadTotalItems = resp.total_items;
          this.threadTotalPage = resp.total_pages;
          // if difference between total pages capacity and length of comment thread list is less than one page capacity
          // replace the last set of data with incoming data
          // using splice to replace
          if (
            this.threadPage * this.threadSize - this.commentThread.length <
            this.threadSize
          ) {
            this.commentThread.splice(
              (this.threadPage - 1) * this.threadSize,
              this.commentThread.length -
                (this.threadPage - 1) * this.threadSize,
              ...resp.result
            );
          } else {
            this.commentThread = [...this.commentThread, ...resp.result];
          }
        },
        error: (HttpResponse: HttpErrorResponse) => {
          this.commentsLoading = false;
           this.snackbarService.openSnackBar(`${HttpResponse.error.detail}`, 'OK');
        },
      });
  };
  choices: User[] = [];
  mentions: ChoiceWithIndices[] = [];
  mentionUsers: any[];
  taggedUsersByUsername: any[] = [];
  taggedUsersByName: any[] = [];
  taggedUsers: any[] = [];
  defSuggestionDisplayCount: number = 3;
  comments: any[] = [];
  activeCommentIndex = 1;
  /**
   * post comment
   * @param comment comment
   * @param i index
   */
  postComment = (comment, i) => {
    let modifiedComment = this.removeAllTagsFromComment(
      comment,
      this.taggedUsersByName
    );
    let obj = {
      comment: modifiedComment.trim(),
      tagged_users: this.taggedUsersByUsername,
    };
    this.commentsService
      .postComment(
        this.subscriptionId,
        this.commentsListForType,
        this.rowId,
        obj
      )
      .subscribe({
        next: (resp) => {
          // work around to remove highlighted tag in textarea
          let elements = document.getElementsByClassName(
            'flx-text-highlight-tag'
          );
          while (elements.length > 0) elements[0].remove();
          this.comments[i] = '';
          this.mentions = [];
          this.taggedUsers = [];
          this.taggedUsersByName = [];
          this.taggedUsersByUsername = [];
           this.snackbarService.openSnackBar(resp.detail, 'OK');
          this.threadTotalItems = this.threadTotalItems + 1;
          this.onCommentThreadScroll();
        },
        error: (HttpResponse: HttpErrorResponse) => {
          this.dataLoading = false;
           this.snackbarService.openSnackBar(`${HttpResponse.error.detail}`, 'OK');
        },
      });
  };

  /**
   * tag selection changes
   * @param choices
   */
  onSelectedChoicesChange(choices: ChoiceWithIndices[]): void {
    this.mentions = choices;
    this.taggedUsers = choices.map((selection) => selection.choice);
    this.taggedUsersByUsername = this.taggedUsers.map((user) => user.username);
    this.taggedUsersByName = this.taggedUsers.map((user) => user.name);
  }

  /**
   * edit comment
   * @param comment
   */
  editComment = (comment) => {
    // store original values
    comment.edited_text = comment.complete_text;
    comment.edited_tagged_users = comment.tagged_users;
    // deactivate all other comment edits
    this.commentThread
      .filter((item) => item.comment_id != comment.comment_id)
      .forEach((row) => {
        row.editable = false;
      });
  };

  /**
   * get options for user list dropdown
   * @param searchTerm
   * @returns
   */
  async loadChoices(searchTerm: string): Promise<User[]> {
    const users = await this.mentionUsers;
    this.choices = users.filter((user) => {
      const alreadyExists = this.mentions.some(
        (m) => m.choice.name === user.name
      );
      return (
        !alreadyExists &&
        user.name.toLowerCase().indexOf(searchTerm.toLowerCase()) > -1
      );
    });
    return this.choices;
  }

  /**
   * tag selection changes
   * @param choices
   */
  onTaggedUserEdited(choices: ChoiceWithIndices[], comment): void {
    this.mentions = choices;
    comment.edited_tagged_users = choices.map((selection) => selection.choice);
  }

  /**
   * tag display format
   * @param user
   * @returns
   */
  getChoiceLabel = (user: User): string => {
    return `@${user.name}`;
  };

  /**
   * cancel comment editing mode
   * @param comment
   */
  cancelEdit = (comment) => {
    comment.editable = false;
  };

  /**
   * delete comment from comment thread
   * @param comment_id
   * @param index
   */
  deleteComment = (comment_id, index) => {
    this.commentsService
      .deleteComment(this.subscriptionId, comment_id)
      .subscribe({
        next: (resp) => {
           this.snackbarService.openSnackBar(resp.detail, 'OK');
          this.commentThread.splice(index, 1);
        },
        error: (HttpResponse: HttpErrorResponse) => {
          this.dataLoading = false;
           this.snackbarService.openSnackBar(`${HttpResponse.error.detail}`, 'OK');
        },
      });
  };

  /**
   * tag removal event in comment thread
   * @param e
   */
  taggedUserRemovedInCommentEdit = (e) => {};
  /**
   * to remove all tags from comment text
   * @param str
   * @param mapObj
   * @returns
   */
  removeAllTagsFromComment = (str, mapObj) => {
    var re = new RegExp(mapObj.map((item) => '@' + item).join('|'), 'gi');
    return str.replace(re, '');
  };

  /**
   * update existing comment
   * @param comment
   */
  updateComment = (comment) => {
    comment.editable = false;
    let taggedUserListByUsername = comment.edited_tagged_users.map(
      (user) => user.username
    );
    let taggedUserListByName = comment.edited_tagged_users.map(
      (user) => user.name
    );
    let modifiedComment = this.removeAllTagsFromComment(
      comment.edited_text,
      taggedUserListByName
    );
    let obj = {
      text: modifiedComment.trim(),
      tagged_users: taggedUserListByUsername,
    };
    this.commentsService
      .updateComment(this.subscriptionId, comment.comment_id, obj)
      .subscribe({
        next: (resp) => {
          this.mentions = [];
           this.snackbarService.openSnackBar(resp.detail, 'OK');
          // update the comment values on client side
          comment.complete_text = comment.edited_text;
          comment.text = modifiedComment.trim();
          comment.tagged_users = comment.edited_tagged_users;
        },
        error: (HttpResponse: HttpErrorResponse) => {
          this.dataLoading = false;
           this.snackbarService.openSnackBar(`${HttpResponse.error.detail}`, 'OK');
        },
      });
  };
  /**
   * used to highlight existing tagged users in textarea
   * @param comment
   * @returns
   */
  getSelectedChoices = (comment): User[] => {
    return comment.tagged_users;
  };

  /**
   * Update the form fields
   * @param rowId
   * @param bucketValue
   */
  updateBucket = (rowId, bucketValue) => {
    let obj = { bucket: bucketValue.toUpperCase() };
    this.productService
      .bucketUpdate(rowId, obj, this.subscriptionId)
      .subscribe({
        next: (resp) => {
          let urlTree = this.router.parseUrl(this.router.url);
          urlTree.queryParams['bucket'] = bucketValue.toUpperCase();
          this.router.navigateByUrl(urlTree);
          // refresh data
          this.getProductDetailsData(this.row_id);
          // Undo snackbar
          this.matSnackbar.openFromComponent(UndoSnackbarComponent, {
            duration: 30000,
            // passing args as data
            data: {
              response: resp.detail,
              subscriptionId: this.subscriptionId,
              id: rowId,
              onUndo: () => {
                this.getProductDetailsData(this.row_id);
              },
            },
          });
        },
        error: (HttpResponse: HttpErrorResponse) => {
           this.snackbarService.openSnackBar(`${HttpResponse.error.detail}`, 'OK');
        },
      });
  };
}

interface User {
  name: string;
  username: string;
}
