import { DataSource } from '@angular/cdk/collections';
import { HttpErrorResponse } from '@angular/common/http';
import { Component, OnInit, Inject } from '@angular/core';
import {
  MatSnackBarRef,
  MAT_SNACK_BAR_DATA,
  MatSnackBar,
} from '@angular/material/snack-bar';
import { UndoService } from '../../services/undo.service';

@Component({
  selector: 'app-undo-snackbar',
  templateUrl: './undo-snackbar.component.html',
  styleUrls: ['./undo-snackbar.component.scss'],
})
export class UndoSnackbarComponent implements OnInit {
  response: any;
  sub_id: any;
  row_id: any;
  loading: boolean;
  row_id_list: any;
  previousBucket: any;

  constructor(
    public snackBarRef: MatSnackBarRef<UndoSnackbarComponent>,
    @Inject(MAT_SNACK_BAR_DATA)
    public data: any,
    private undoService: UndoService,
    private matSnackbar: MatSnackBar,
  ) {}

  ngOnInit() {
    this.response = this.data.response;
    this.sub_id = this.data.subscriptionId;
    this.row_id = this.data.id;
    this.previousBucket = this.data.previousBucket;
  }

  undoCall = () => {
    this.loading = true;
    this.response = 'PROCESSING..';
    this.undoService.undo(this.sub_id, this.row_id).subscribe({
      next: (resp) => {
        this.loading = false;
        this.response = resp.detail;
        this.data.onUndo();
        this.matSnackbar.open(resp.detail, 'OK', {
          duration: 3000,
        });
      },
      error: (HttpResponse: HttpErrorResponse) => {
        this.loading = false;
        this.response = this.data.response;
        this.matSnackbar.open(`${HttpResponse.error.detail}`, 'OK', {
          duration: 3000,
        });
      },
    });
  };
}

