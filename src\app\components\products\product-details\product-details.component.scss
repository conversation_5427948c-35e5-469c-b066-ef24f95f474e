@import "../../../../styles/variables";

.loading-spinner {
  margin-top: 300px;
}
.filter-container {
  .filter-head {
    z-index: 1000;
    .back-icon {
      margin-top: -1px;
      padding-right: 10px;
      cursor: pointer;
    }
    .product-header {
      span {
        font-family: $site-font;
        font-style: normal;
        font-weight: 600;
        font-size: 16px;
        line-height: 24px;
      }
      img {
        padding-left: 10px;
        width: 24px;
        height: 24px;
      }
    }

    .action-btn {
      button {
        height: 40px;
        border-radius: 4px;
        font-weight: 600;
        font-size: 14px;
        line-height: 21px;
        //   padding-right: 10px;
      }
      .update-btn {
        background-color: #52bd94;
        color: $theme-white;
      }
      .previous-btn {
        background: #e6e8f0;
        color: $theme-black;
      }
    }
  }
}

.product-info-container {

  width: 100%;
  box-sizing: border-box;
  background: #ffffff;
  border-radius: 4px;
  box-shadow: none;

  .product-image {
    img {
      width: 106px;
      height: 108px;
      border-radius: 4px;
      cursor: pointer;
    }
    .product-details {
      // padding-left: 40px;
      // padding-top: 10px;
      p {
        font-style: normal;
        font-size: 14px;
        line-height: 21px;
        padding-bottom: 10px;
      }
      .bold {
        font-weight: 600;
        color: $theme-black;
      }
      .tag {
        margin-top: -10px;
        width: fit-content;
        height: 21px;
        text-align: center;
        border-radius: 40px;
        p {
          margin-left: 10px;
          margin-right: 10px;
          color: $theme-white;
          //   padding: 5px;
          font-style: normal;
          font-weight: normal;
          font-size: 10px;
        }
      }
    }
    .product-details-row {
      // overflow-x: scroll;  
      ul {
        list-style-type: none;
        columns: 1;
    
        -webkit-columns: 1;
        -moz-columns: 1;
        height: 100px;
     
    }

      p {
        font-style: normal;
        font-size: 14px;
        line-height: 21px;
        padding-bottom: 10px;
        padding-left: 20px;
      }
      .bold {
        font-weight: 600;
        color: $theme-black;
      }
    }
  }
  .prod-img {
    // margin-left: 2%;
    width: 109px;
    height: 109px;
    border-radius: 5%;
    img {
      border: 1px solid #e0e0e0;
      border-radius: 4px;
    }
    span.overlay {
      h3 {
        position: sticky;
        display: block;
        background-color: black;
        cursor: pointer;
        margin: -108px 2px 0 0px;
        color: #fff;
        font-weight: 700;
        opacity: 0.55;
        padding: 45px 0 0 0;
        width: 109px;
        height: 63px;
        border-radius: 3px;
        text-align: center;
      }
    }
  }
  .prod-ids {
    margin-left: 30px;
    height: 110px;
    font: 400 14px/20px Roboto, "Helvetica Neue", sans-serif;
  }
  .image-count {
    display: inline-block;
    margin-left: 35px;
  }
}

.vertical-tab-container {
  margin-top: 15px;
  width: 100%;
  // height: calc(100vh - 320px) !important;
 
  // overflow-y: scroll;
  .product-field {
    
    margin-left: 15px;
    background: #ffffff;
    border-radius: 4px;
    min-height: 625px;
    // height: 350px;
    // overflow-y: scroll;
    .tab-attr {
      width: 90%;
      .form-field {
        width: 100%;
        p {
          font-style: normal;
          font-weight: 600;
          font-size: 14px;
          // line-height: 21px;
          margin-bottom: -0.5px;
        }
        mat-form-field {
          width: 70%;
          border: 1px solid #e6e8f0;
          box-sizing: border-box;
          border-radius: 4px;
          height: 40px;
          margin-bottom: 15px;
          input {
            margin-top: -10px;
            padding-left: 10px;
          }
          img {
            margin-top: -5px;
            padding-right: -10px;
            cursor: pointer;
          }
        }
      }
    }
  }
}

:host ::ng-deep {
  .mat-tab-labels {
    flex-direction: column;
    color: white !important;
    text-align: left !important;
  }
  // .mat-tab-label-active {
  //  .active {
  //   color: white !important;
  //   text-align: left !important;
  //   opacity: 1;
  //  }
  // }

  .mat-tab-group {
    flex-direction: row;
    text-align: left !important;
  }

  .mat-tab-header {
    border-bottom: none;
    background-color: #ffffff;
    text-align: left !important;
  }
  .mat-tab-body-wrapper {
    flex: 1 1 auto;
    text-align: left !important;
  }
}

.mat-tab-label-container {
  border-radius: 4px;
  overflow: scroll !important;
}

// ::ng-deep .mat-tab-group.mat-primary .mat-ink-bar {
//   height: 4px;
//   border-radius: 4px 4px 0px 0px;
// }

.product-detail-comments {
  /* width */
::-webkit-scrollbar {
  width: 0px;
}


  height: 300px;
  width: 100%;
  margin-bottom: 10px;
  padding: 20px 10px;
  overflow-y: scroll;
  border-radius: 4px;
  display: flex;
  flex-direction: column-reverse;
  background-color: $neutral-10;
  box-shadow: 0px 1px 6px #34343429;
  .cmt-profile {
    width: 100%;
    img {
      width: 32px;
      height: 32px;
      border-radius: 5px;
    }
  }
  .cmt-profile-picture {
    width: 80%;
    font-style: normal;
    color: $theme-black;
    padding-left: 10px;
    .comment-head {
      img {
        width: 16px;
        height: 16px;
        cursor: pointer;
      }
      .username {
        font-weight: 600;
        font-size: 12px;
        line-height: 18px;
        padding-bottom: -10px;
      }
      .comment-time {
        margin-top: -10px;
        font-weight: 300;
        font-size: 10px;
        line-height: 15px;
      }
    }
    .comments {
      width: 100%;
      cursor: pointer;
      padding: 6px 10px 6px 6px;
      font-family: normal normal normal 14px/21px "Open Sans", sans-serif !important;
      border-radius: 8px;
      // &:hover {

      // }
      img {
        width: 20px;
        height: 20px;
      }
    }
    .hover-color {
      background: #f4f6fa;
    }
    .edit-comment-box {
        /* width */
        ::-webkit-scrollbar {
          width: 0px;
        }

      border: 1px solid #e6e8f0;
      border-radius: 4px;
      padding-right: 10px;
      .edit-comment-text {
        width: 80%;
        padding: 10px;
        resize: none;   
         
      }
      ul {
        width: 13rem;
        background-color: #fff;
        box-shadow: 0 2px 2px 0 rgb(0 0 0 / 14%),
          0 3px 1px -2px rgb(0 0 0 / 20%), 0 1px 5px 0 rgb(0 0 0 / 12%);
        padding: 0;
        li {
          padding-left: 1rem;
          padding-right: 1rem;
          height: 2rem;
          min-width: 10rem;
          background-color: #fff;
          cursor: pointer;
          display: -webkit-box;
          display: flex;
          -webkit-box-align: center;
          align-items: center;
          -webkit-box-pack: justify;
          justify-content: space-between;
        }
      }
      img {
        cursor: pointer;
        width: 24px;
        width: 24px;
      }
      mat-icon {
        cursor: pointer;
      }
    }
  }
  .comment-box {
    bottom: 30px;
    border: 1px solid #e6e8f0;
    box-sizing: border-box;
    border-radius: 4px;
    width: 90%;
    padding: 20px;
    background: #fff;
    border-radius: 4px;
    // padding: 1rem;
    ul {
      width: 13rem;
      background-color: #fff;
      box-shadow: 0 2px 2px 0 rgb(0 0 0 / 14%), 0 3px 1px -2px rgb(0 0 0 / 20%),
        0 1px 5px 0 rgb(0 0 0 / 12%);
      padding: 0;
      li {
        padding-left: 1rem;
        padding-right: 1rem;
        height: 2rem;
        min-width: 10rem;
        background-color: #fff;
        cursor: pointer;
        display: -webkit-box;
        display: flex;
        -webkit-box-align: center;
        align-items: center;
        -webkit-box-pack: justify;
        justify-content: space-between;
      }
    }
    .cmt-textarea {
      width: 100%;
      img {
        cursor: pointer;
        margin-right: 6px;
      }
      textarea {
        resize: none;
      }
    }
    .add-file {
      margin-bottom: -12px;
      margin-left: 10px;
      cursor: pointer;
    }
  }
}

.view-messages-icon{
  cursor: pointer;
}