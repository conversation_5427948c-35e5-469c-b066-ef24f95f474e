import { Component, OnInit, Input } from '@angular/core';
import { CommentsService } from 'src/app/services/comments.service';
import { ActivatedRoute, Router, Params } from '@angular/router';
import { HttpErrorResponse } from '@angular/common/http';
import { MatSnackBar } from '@angular/material/snack-bar';
import { ChoiceWithIndices } from '@flxng/mentions';
import { interval as observableInterval } from 'rxjs';
import { takeWhile, scan, tap } from 'rxjs/operators';
import { SnackbarService } from '../../services/snackbar.service';

interface User {
  name: string;
  username: string;
}
@Component({
  selector: 'app-comments',
  templateUrl: './comments.component.html',
  styleUrls: ['./comments.component.scss'],
})
export class CommentsComponent implements OnInit {
  subscriptionId;
  page: number = 1;
  size: number = 10;
  batchId;
  rowId;
  commentsListForType;
  totalItems;
  totalPage;
  dataLoading: boolean;
  commentsLoading: boolean;
  commentsList: any[] = [];
  search;
  searchedItem: string = '';
  activeCommentIndex = 1;
  selectedCommentId;
  userNameList: string[] = [];
  comments: any[] = [];
  taggedUsersByUsername: any[] = [];
  taggedUsersByName: any[] = [];
  taggedUsers: any[] = [];
  mentionUsers: any[];
  queryParams;
  commentThread: any[] = [];
  threadPage: number = 1;
  threadSize: number = 10;
  threadTotalItems: number;
  threadTotalPage: number;
  commentIsResolved: boolean;
  row: number;
  commentThreadTitle;
  userData;
  text = ``;
  loading = false;
  choices: User[] = [];
  mentions: ChoiceWithIndices[] = [];
  selectedValForToggle;
  originFrom;
  row_id;
  bucket;
  categoryId;

  constructor(
    private router: Router,
    private route: ActivatedRoute,
    private commentsService: CommentsService,
    private matSnackbar: MatSnackBar,
    private snackbarService: SnackbarService
  ) {}

  ngOnInit(): void {
    this.dataLoading = true;
    this.commentsLoading = true;
    // get batch id if any from route
    this.route.queryParams.subscribe((params: Params) => {
      this.batchId = params.batch_id;
      this.originFrom = params.origin;
      this.row_id = params.row_id;
      this.bucket = params.bucket;
      // toggle button as per the batch/sku id or no id
      if (this.batchId) {
        this.commentsListForType = 'batch';
        this.categoryId = this.batchId;
      } else if (this.row_id) {
        this.commentsListForType = 'row';
        this.categoryId = this.row_id;
      } else {
        this.commentsListForType = 'batch';
        this.categoryId = null;
      }
    });
    // add subscription id from local storage in url as param
    this.subscriptionId = localStorage.getItem('SubscriptionID');
    this.router.navigate([], {
      relativeTo: this.route,
      queryParams: { sub: this.subscriptionId },
      queryParamsHandling: 'merge', // remove to replace all query params by provided
    });
    // get user data from local storage
    if (localStorage.getItem('user')) {
      this.userData = JSON.parse(localStorage.getItem('user'));
    }
    // get user name List
    this.getUserNameList();
    // get comments list
    this.getCommentsList(
      this.subscriptionId,
      this.page,
      this.size,
      this.commentsListForType,
      this.categoryId,
      this.searchedItem,
      false
    );
  }

  /**
   * get options for user list dropdown
   * @param searchTerm
   * @returns
   */
  async loadChoices(searchTerm: string): Promise<User[]> {
    const users = await this.mentionUsers;
    this.choices = users.filter((user) => {
      const alreadyExists = this.mentions.some(
        (m) => m.choice.name === user.name
      );
      return (
        !alreadyExists &&
        user.name.toLowerCase().indexOf(searchTerm.toLowerCase()) > -1
      );
    });
    return this.choices;
  }

  /**
   * tag display format
   * @param user
   * @returns
   */
  getChoiceLabel = (user: User): string => {
    return `@${user.name}`;
  };

  /**
   * tag selection changes
   * @param choices
   */
  onSelectedChoicesChange(choices: ChoiceWithIndices[]): void {
    this.mentions = choices;
    this.taggedUsers = choices.map((selection) => selection.choice);
    this.taggedUsersByUsername = this.taggedUsers.map((user) => user.username);
    this.taggedUsersByName = this.taggedUsers.map((user) => user.name);
  }

  /**
   * tag selection changes
   * @param choices
   */
  onTaggedUserEdited(choices: ChoiceWithIndices[], comment): void {
    this.mentions = choices;
    comment.edited_tagged_users = choices.map((selection) => selection.choice);
  }

  /**
   * On button toggle change
   * @param val
   */
  onButtonToggle = (val) => {
    this.commentsListForType = val;
    this.searchedItem = '';
    this.commentsList = [];
    this.commentThread = [];
    this.dataLoading = true;
    this.page = 1;
    this.threadPage = 1;
    this.categoryId = null;
    // update route url to remove any specific row id/batch id
    this.router.navigate([], {
      relativeTo: this.route,
      queryParams: { origin: this.originFrom, sub: this.subscriptionId },
    });
    this.getCommentsList(
      this.subscriptionId,
      this.page,
      this.size,
      this.commentsListForType,
      this.categoryId,
      this.searchedItem,
      false
    );
  };

  /**
   * reset search keyword
   */
  resetSearch = () => {
    this.searchedItem = '';
    this.search = '';
    this.getCommentsList(
      this.subscriptionId,
      this.page,
      this.size,
      this.commentsListForType,
      this.categoryId,
      this.searchedItem,
      false
    );
  };

  /**
   * get comment list from searched item in search bar
   * @param q
   */
  getSearchedItem = (q) => {
    this.searchedItem = q.trim();
    this.getCommentsList(
      this.subscriptionId,
      this.page,
      this.size,
      this.commentsListForType,
      this.categoryId,
      this.searchedItem,
      false
    );
  };

  /**
   * get active/highlighted comment index from side div
   * @param i
   * @param id
   */
  getActiveCommentIndex = (i, comment) => {
    this.activeCommentIndex = i + 1;
    // empty previously tagged names
    this.mentions = [];
    this.selectedCommentId = comment.category_id;
    this.commentIsResolved = comment.is_resolved;
    // show comment thread loader
    this.commentsLoading = true;
    this.getCommentThread(
      this.subscriptionId,
      this.threadPage,
      this.threadSize,
      this.commentsListForType,
      this.selectedCommentId,
      this.searchedItem,
      true
    );
  };

  /**
   * get list of commemts to display on side div
   * @param subs_id
   * @param page
   * @param size
   * @param category
   * @param category_id
   * @param q
   * @param comment_thread
   */
  getCommentsList = (
    subs_id,
    page,
    size,
    category,
    category_id,
    q,
    comment_thread
  ) => {
    // reset active comment index to 1
    this.activeCommentIndex = 1;
    this.commentsService
      .getCommentsList(
        subs_id,
        page,
        size,
        category,
        category_id,
        q,
        comment_thread
      )
      .subscribe({
        next: (resp) => {
          this.dataLoading = false;
          this.commentsList = [];
          this.commentsList = [...this.commentsList, ...resp.result];
          this.page = resp.page;
          this.size = resp.page_size;
          this.totalItems = resp.total_items;
          this.totalPage = resp.total_pages;
          this.selectedCommentId = this.commentsList[0].category_id;
          this.commentIsResolved = this.commentsList[0].is_resolved;
          this.getCommentThread(
            subs_id,
            this.threadPage,
            this.threadSize,
            category,
            this.selectedCommentId,
            this.searchedItem,
            true
          );
        },
        error: (HttpResponse: HttpErrorResponse) => {
          this.dataLoading = false;
          this.snackbarService.openSnackBar(`${HttpResponse.error.detail}`, 'OK');
        },
      });
  };

  /**
   * get comment thread for respective sku/batch comment
   * @param subs_id
   * @param page
   * @param size
   * @param category
   * @param batch_or_row_id
   * @param q
   * @param comment_thread
   */
  getCommentThread = (
    subs_id,
    page,
    size,
    category,
    batch_or_row_id,
    q,
    comment_thread
  ) => {
    this.commentsService
      .getCommentsList(
        subs_id,
        page,
        size,
        category,
        batch_or_row_id,
        q,
        comment_thread
      )
      .subscribe({
        next: (resp) => {
          this.commentsLoading = false;
          // this.commentThread = [...this.commentThread, ...resp.result];
          // this.commentThread = resp.result;

          this.threadPage = resp.page;
          this.threadSize = resp.page_size;
          this.threadTotalItems = resp.total_items;
          this.threadTotalPage = resp.total_pages;
          this.commentThreadTitle = resp.title;
          // if difference between total pages capacity and length of comment thread list is less than one page capacity
          // replace the last set of data with incoming data
          // using splice to replace
          if (
            this.threadPage * this.threadSize - this.commentThread.length <
            this.threadSize
          ) {
            this.commentThread.splice(
              (this.threadPage - 1) * this.threadSize,
              this.commentThread.length -
                (this.threadPage - 1) * this.threadSize,
              ...resp.result
            );
          } else {
            this.commentThread = [...this.commentThread, ...resp.result];
          }
        },
        error: (HttpResponse: HttpErrorResponse) => {
          this.commentsLoading = true;
          this.snackbarService.openSnackBar(`${HttpResponse.error.detail}`, 'OK');
        },
      });
  };

  /**
   * post comment
   * @param comment comment
   * @param i index
   */
  postComment = (comment, i) => {
    let modifiedComment = this.removeAllTagsFromComment(
      comment,
      this.taggedUsersByName
    );
    let obj = {
      comment: modifiedComment.trim(),
      tagged_users: this.taggedUsersByUsername,
    };
    this.commentsService
      .postComment(
        this.subscriptionId,
        this.commentsListForType,
        this.selectedCommentId,
        obj
      )
      .subscribe({
        next: (resp) => {
          // work around to remove highlighted tag in textarea
          let elements = document.getElementsByClassName(
            'flx-text-highlight-tag'
          );
          while (elements.length > 0) elements[0].remove();
          this.comments[i] = '';
          this.mentions = [];
          this.taggedUsers = [];
          this.taggedUsersByName = [];
          this.taggedUsersByUsername = [];
          // this.matSnackbar.open(resp.detail, 'OK', {
          //   duration: 3000,
          // });
          this.snackbarService.openSnackBar(resp.detail, 'OK');
          this.threadTotalItems = this.threadTotalItems + 1;
          this.onCommentThreadScroll();
        },
        error: (HttpResponse: HttpErrorResponse) => {
          this.dataLoading = false;
          // this.matSnackbar.open(`${HttpResponse.error.detail}`, 'OK', {
          //   duration: 3000,
          // });
          this.snackbarService.openSnackBar(`${HttpResponse.error.detail}`, 'OK');
        },
      });
  };

  /**
   * to remove all tags from comment text
   * @param str
   * @param mapObj
   * @returns
   */
  removeAllTagsFromComment = (str, mapObj) => {
    var re = new RegExp(mapObj.map((item) => '@' + item).join('|'), 'gi');
    return str.replace(re, '');
  };

  /**
   * get user list for tagging in comments
   */
  getUserNameList = () => {
    this.commentsService.getUserNamesToTag(this.subscriptionId).subscribe({
      next: (resp) => {
        this.mentionUsers = resp.result;
        // return resp;
      },
    });
  };

  /**
   * resolve comment
   */
  resolveComment = () => {
    this.commentsService
      .resolveComment(
        this.subscriptionId,
        this.commentsListForType,
        this.selectedCommentId
      )
      .subscribe({
        next: (resp) => {
          // this.matSnackbar.open(resp.detail, 'OK', {
          //   duration: 3000,
          // });
          this.snackbarService.openSnackBar(resp.detail, 'OK');
          // mark the selected batch/sku comments resolved as true
          this.commentsList.filter(
            (comment) => comment.category_id == this.selectedCommentId
          )[0].is_resolved = true;
          this.commentIsResolved = true;
        },
        error: (HttpResponse: HttpErrorResponse) => {
          this.dataLoading = false;
          this.snackbarService.openSnackBar(`${HttpResponse.error.detail}`, 'OK');
        },
      });
  };

  /**
   * event fired when comment preview section is scrolled
   * fetching new pages
   */
  onScrollDown() {
    this.page++;
    if (this.page <= this.totalPage) {
      this.commentsService
        .getCommentsList(
          this.subscriptionId,
          this.page,
          this.size,
          this.commentsListForType,
          this.categoryId,
          this.searchedItem,
          false
        )
        .subscribe({
          next: (resp) => {
            this.commentsList = [...this.commentsList, ...resp.result];
            this.page = resp.page;
            this.size = resp.page_size;
            this.totalItems = resp.total_items;
            this.totalPage = resp.total_pages;
          },
        });
    }
  }

  onUp = () => {
    if (this.threadPage > 1) {
      this.threadPage--;
    }
    this.commentsService
      .getCommentsList(
        this.subscriptionId,
        this.threadPage,
        this.threadSize,
        this.commentsListForType,
        this.selectedCommentId,
        this.searchedItem,
        true
      )
      .subscribe({
        next: (resp) => {
          this.commentsLoading = false;
          this.commentThread = resp.result;
          this.threadPage = resp.page;
          this.threadSize = resp.page_size;
          this.threadTotalItems = resp.total_items;
          this.threadTotalPage = resp.total_pages;
          // if all the data on the page is deleted/lost
          if (this.commentThread.length == 0) {
            this.onUp();
          }
        },
        error: (HttpResponse: HttpErrorResponse) => {
          this.commentsLoading = true;
          this.snackbarService.openSnackBar(`${HttpResponse.error.detail}`, 'OK');
        },
      });
  };

  /**
   * event fired when comment thread section is scrolled
   * fetching new pages
   */
  onCommentThreadScroll = () => {
    // check if comment thread length is equal to total item capacity of pages
    // or check if total item size > total item capacity of pages
    // increment pageSize by 1
    if (this.threadTotalItems > this.commentThread.length) {
      if (this.commentThread.length == this.threadSize * this.threadPage) {
        this.threadPage++;
        this.getCommentThread(
          this.subscriptionId,
          this.threadPage,
          this.threadSize,
          this.commentsListForType,
          this.selectedCommentId,
          this.searchedItem,
          true
        );
      } else {
        this.getCommentThread(
          this.subscriptionId,
          this.threadPage,
          this.threadSize,
          this.commentsListForType,
          this.selectedCommentId,
          this.searchedItem,
          true
        );
      }
    }
  };

  scrollToTop(el) {
    const duration = 600;
    const interval = 5;
    const move = (el.scrollTop * interval) / duration;
    observableInterval(interval)
      .pipe(
        scan((acc, curr) => acc - move, el.scrollTop),
        tap((position) => (el.scrollTop = position)),
        takeWhile((val) => val > 0)
      )
      .subscribe();
  }

  /**
   * used to highlight existing tagged users in textarea
   * @param comment
   * @returns
   */
  getSelectedChoices = (comment): User[] => {
    return comment.tagged_users;
  };

  /**
   * edit comment
   * @param comment
   */
  editComment = (comment) => {
    // store original values
    comment.edited_text = comment.complete_text;
    comment.edited_tagged_users = comment.tagged_users;
    // deactivate all other comment edits
    this.commentThread
      .filter((item) => item.comment_id != comment.comment_id)
      .forEach((row) => {
        row.editable = false;
      });
  };

  /**
   * cancel comment editing mode
   * @param comment
   */
  cancelEdit = (comment) => {
    comment.editable = false;
  };

  /**
   * delete comment from comment thread
   * @param comment_id
   * @param index
   */
  deleteComment = (comment_id, index) => {
    this.commentsService
      .deleteComment(this.subscriptionId, comment_id)
      .subscribe({
        next: (resp) => {
          this.snackbarService.openSnackBar(resp.detail, 'OK');
          this.commentThread.splice(index, 1);
          // this.getCommentThread(
          //   this.subscriptionId,
          //   this.threadPage,
          //   this.threadSize,
          //   this.commentsListForType,
          //   this.selectedCommentId,
          //   this.searchedItem,
          //   true
          // );
        },
        error: (HttpResponse: HttpErrorResponse) => {
          this.dataLoading = false;
          this.snackbarService.openSnackBar(`${HttpResponse.error.detail}`, 'OK');
        },
      });
  };

  /**
   * tag removal event in comment thread
   * @param e
   */
  taggedUserRemovedInCommentEdit = (e) => {};

  /**
   * update existing comment
   * @param comment
   */
  updateComment = (comment) => {
    comment.editable = false;
    let taggedUserListByUsername = comment.edited_tagged_users.map(
      (user) => user.username
    );
    let taggedUserListByName = comment.edited_tagged_users.map(
      (user) => user.name
    );
    let modifiedComment = this.removeAllTagsFromComment(
      comment.edited_text,
      taggedUserListByName
    );
    let obj = {
      text: modifiedComment.trim(),
      tagged_users: taggedUserListByUsername,
    };
    this.commentsService
      .updateComment(this.subscriptionId, comment.comment_id, obj)
      .subscribe({
        next: (resp) => {
          this.mentions = [];
          this.snackbarService.openSnackBar(resp.detail, 'OK');
          // update the comment values on client side
          comment.complete_text = comment.edited_text;
          comment.text = modifiedComment.trim();
          comment.tagged_users = comment.edited_tagged_users;
        },
        error: (HttpResponse: HttpErrorResponse) => {
          this.dataLoading = false;
          this.snackbarService.openSnackBar(`${HttpResponse.error.detail}`, 'OK');
        },
      });
  };
}
