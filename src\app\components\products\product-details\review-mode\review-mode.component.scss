@import "../../../../../styles/variables";
.search-container {
  mat-form-field {
    font-style: normal;
    font-weight: normal;
    font-size: 14px;
    line-height: 21px;
    .search-icon {
      cursor: pointer;
      margin-top: -10px;
      font-style: normal;
      font-weight: normal;
      font-size: 14px;
      line-height: 21px;
      // font-family: $site-font;
    }
    width: 10px;
    font: $site-font;
    input {
      width: 90% !important;
      ::placeholder {
        margin-left: 10px;
        font-style: normal;
        font-weight: 300;
        line-height: 14px;
      }
    }
  }
  .toggle{
    font-style: normal;
    font-weight: normal;
    font-size: 14px;
    font-weight: 15px;
    line-height: 21px;
  }
}

.product-filter {
  width: auto;
  height: 40px;
  border: 1px solid #c1c4d6;
  box-sizing: border-box;
  border-radius: 4px;

  .mat-select {
    width: 92%;
    padding-left: 10px;
    position: absolute;
    margin-top: -10px;
    .mat-select-placeholder {
      color: #3b3e48;
    }
    .mat-select-arrow {
      color: #3b3e48;
    }
  }
}

.previous-btn {
  font-style: normal;
  font-weight: 600;
  font-size: 16px;
  line-height: 24px;
  width: auto;
  height: 40px;
  background: #e6e8f0;
  border-radius: 4px;
  mat-icon {
    // margin-left: -10px;
    margin-top: -2px;
    // padding: 5px;
    color: #3b3e48;
  }
}
.view-messages-icon {
  color: #3b3e48;
  cursor: pointer;
  display: flex;
  height: 40px;
  align-items: center;
  .dot {
    position: relative;
    height: 8px;
    width: 8px;
    background-color: #d30505;
    border-radius: 50%;
    top: 10px;
    left: 16px;
  }
}
.chip-wrapper {
  margin-left: 20px;
  margin-bottom: 10px;
}
.table-wrapper {
  .product-count {
    font-family: "Montserrat";
    margin-bottom: 15px;
    span.current-item {
      font-size: 14px;
      margin-right: 3px;
    }
    span.total-items {
      font-size: 20px;
    }
  }
  .review-info {
    font-family: $site-font;
    font-style: normal;
    font-weight: 600;
    font-size: 16px;
    line-height: 24px;
    color: $theme-black;
    #stat {
      color: #e84545;
    }
  }
}

.review-header {
  height: 50px;
  background-color: #edeff5;
  background: #edeff5;
  border-radius: 4px 4px 0px 0px;
  .head {
    margin-top: 15px;
    width: 118px;
    height: 18px;
    text-align: left;
    font-family: $site-font;
    font-style: normal;
    font-weight: 600;
    font-size: 16px;
    line-height: 24px;
  }
}
.no-review-data {
  padding: 100px;
}
.review-viewport {
  .review-page-main-card {
    flex-basis: 100%;
    .product-details {
      .product-images {
        img {
          width: 64px;
          height: 64px;
          margin-right: 10px;

          margin-bottom: -15px;
          cursor: pointer;
        }
        span.overlay {
          h3 {
            position: absolute;
            display: block;
            background-color: black;
            cursor: pointer;
            top: 4%;
            color: #fff;
            font-weight: 700;
            width: pa;
            padding: 16px 23px;
            opacity: 0.55;
            border-radius: 3px;
          }
        }
        .tag {
          // background: #1475eb;
          border-radius: 20px;
          color: #fff;
          font-weight: 400;
          text-align: center;
          transform: scale(0.7);
          padding: 5px;
          font-size: 11px !important;
          margin: 10px -10px;
        }
      }
      .title {
        margin-bottom: 5px;
        p {
          margin: 0;
        }
        .product-caption {
          width: 100%;
          cursor: pointer;
          text-align: left;
          text-decoration: underline;
          font: normal normal 600 14px/24px $site-font;
          letter-spacing: 0px;
          opacity: 1;
        }
        .product-title {
          font: normal normal 500 14px/24px $site-font;
          letter-spacing: 0px;
          opacity: 1;
        }
        a.product-url {
          word-break: break-word;
        }

        .ref-url {
          display: flex;
          align-items: center;
          .url-icon {
            margin-left: 5px;
            cursor: pointer;
          }
        }
        .product-description {
          width: 100%;
          height: 100%;
          position: relative;
          text-align: left;
          font: normal normal normal 14px/21px "Open Sans", sans-serif;
          letter-spacing: 0px;
          color: #646464;
          margin-bottom: 0;
          //view more
          .view-more {
            width: 65px;
            text-align: left;
            font: normal normal 600 14px/21px $site-font;
            letter-spacing: 0px;
            text-decoration: none;
          }
        }
      }
      .file-download {
        position: relative;
        margin-bottom: 20px;
        img {
          width: 24px;
          height: 24px;
        }
        .icon-container {
          a {
            padding: 5px;
          }
        }
      }
      .action-btn {
        width: 100%;
        button {
          height: 32px;
          border-radius: 4px;
          padding: 5px;
          font-weight: 600;
        }
      }
    }

    // suggestion and edit card
    .suggestion-edit-section {
      // suggestion accept/decline block
      .no-review-suggestions {
        color: #969595;
        height: 200px;
      }
      .suggestion-info {
        width: 100%;
        .accept-info {
          border: 1px solid rgb(255, 255, 255);
          background-color: #03962837;
          border-radius: 5px;
          width: 100%;
          height: 18px;
          color: var(--primary-200-default);
          text-align: center;
          font: normal normal 600 14px/18px "Open Sans", sans-serif;
          letter-spacing: 0px;
          color: #039628;
          opacity: 1;
        }
        .decline-info {
          border: 1px solid rgb(255, 255, 255);
          background-color: #f1082732;
          border-radius: 5px;
          width: 100%;
          height: 18px;
          color: var(--primary-200-default);
          text-align: center;
          font: normal normal 600 14px/18px "Open Sans", sans-serif;
          letter-spacing: 0px;
          color: #f10827;
          opacity: 1;
        }
      }
      .suggestion-edit-card {
        margin-bottom: 20px;
        width: 90%;
        border: 1px solid #e4e8ee;
        border-radius: 4px;
        opacity: 1;
        box-shadow: none !important;
        .attribute-name {
          .p {
            width: 100%;
            height: 15px;
            color: var(--light-on-primary-mid-emphasis);
            text-align: left;
            font: normal normal 400 14px/21px "Open Sans", sans-serif;
            letter-spacing: 0px;
            color: #646464;
            opacity: 1;
          }
        }
        // hr line
        .existing-section {
          .hr-line:before,
          .hr-line:after {
            content: "";
            flex: 1 1;
            border: 1px solid #e4e8ee;
            opacity: 1;
            width: 388px;
          }
          .hr-line {
            margin-top: 10px;
            width: 100%;
            display: flex;
            flex-direction: row;
          }
          margin-top: 10px;
          //existing section
          .attr-existing {
            width: 50px;
            height: 15px;
            text-align: left;
            font: normal normal 600 14px/21px "Open Sans", sans-serif;
            letter-spacing: 0px;
            color: $orange-200;
            opacity: 1;
          }
          //suggestion section
          .attr-suggestion {
            margin-top: 10px;
            width: 50px;
            height: 15px;
            text-align: left;
            font: normal normal 600 14px/21px "Open Sans", sans-serif;
            letter-spacing: 0px;
            color: $theme-green;
            opacity: 1;
          }
          //existing & suggestion value
          .attr-value {
            margin-bottom: 20px;
            width: 80%;
            // height: 15px;
            word-break: break-word;
            font: normal normal 600 14px/21px "Open Sans", sans-serif;
            letter-spacing: 0px;
            color: $theme-black;
            opacity: 1;
          }
          .action-btn {
            float: right;
            margin-top: 0px;
            img {
              cursor: pointer;
            }
          }
        }
      }
      .active-selection {
        outline: 1px solid $theme-button;
        -moz-outline-radius: 4px;
      }
      .suggestion-btns {
        button {
          cursor: pointer;
          margin-right: 2%;
          width: 100%;
          height: 25px;
          border: 1px solid #f7f7f7;
          border-radius: 4px;
          opacity: 1;
          font: normal normal 600 11px/20px Montserrat;
          letter-spacing: 0px;
          color: white;
          opacity: 1;
          margin-bottom: 5px;
        }
        .accept-all {
          background: $theme-green 0% 0% no-repeat padding-box;
        }
        .reject-all {
          background: rgb(235, 20, 20) 0% 0% no-repeat padding-box;
        }
      }
      .more-suggestions-btn {
        color: #1475eb;
        text-align: center;
        width: 100%;
      }
    }

    .review-comments {
      width: 100%;
      margin-bottom: 10px;
      padding: 20px 10px;
      height: 400px;
      overflow-y: scroll;
      border-radius: 4px;
      display: flex;
      flex-direction: column-reverse;
      background-color: $neutral-10;
      .cmt-profile {
        width: 100%;
        img {
          width: 32px;
          height: 32px;
          border-radius: 5px;
        }
      }
      .cmt-profile-picture {
        width: 80%;
        font-style: normal;
        color: $theme-black;
        padding-left: 10px;
        .comment-head {
          img {
            width: 16px;
            height: 16px;
            cursor: pointer;
          }
          .username {
            font-weight: 600;
            font-size: 12px;
            line-height: 18px;
            padding-bottom: -10px;
          }
          .comment-time {
            margin-top: -10px;
            font-weight: 300;
            font-size: 10px;
            line-height: 15px;
          }
        }
        .comments {
          width: 100%;
          cursor: pointer;
          padding: 6px 10px 6px 6px;
          font-family: normal normal normal 14px/21px "Open Sans", sans-serif !important;
          border-radius: 8px;
          // &:hover {

          // }
          img {
            width: 20px;
            height: 20px;
          }
        }
        .hover-color {
          background: #f4f6fa;
        }
        .edit-comment-box {
          border: 1px solid #e6e8f0;
          border-radius: 4px;
          padding-right: 10px;
          .edit-comment-text {
            width: 80%;
            padding: 10px;
            resize: none;
          }
          ul {
            width: 13rem;
            background-color: #fff;
            box-shadow: 0 2px 2px 0 rgb(0 0 0 / 14%),
              0 3px 1px -2px rgb(0 0 0 / 20%), 0 1px 5px 0 rgb(0 0 0 / 12%);
            padding: 0;
            li {
              padding-left: 1rem;
              padding-right: 1rem;
              height: 2rem;
              min-width: 10rem;
              background-color: #fff;
              cursor: pointer;
              display: -webkit-box;
              display: flex;
              -webkit-box-align: center;
              align-items: center;
              -webkit-box-pack: justify;
              justify-content: space-between;
            }
          }
          img {
            cursor: pointer;
            width: 24px;
            width: 24px;
          }
          mat-icon {
            cursor: pointer;
          }
        }
      }
      .comment-box {
        bottom: 30px;
        border: 1px solid #e6e8f0;
        box-sizing: border-box;
        border-radius: 4px;
        width: 90%;
        padding: 20px;
        background: #fff;
        border-radius: 4px;
        // padding: 1rem;
        ul {
          width: 13rem;
          background-color: #fff;
          box-shadow: 0 2px 2px 0 rgb(0 0 0 / 14%),
            0 3px 1px -2px rgb(0 0 0 / 20%), 0 1px 5px 0 rgb(0 0 0 / 12%);
          padding: 0;
          li {
            padding-left: 1rem;
            padding-right: 1rem;
            height: 2rem;
            min-width: 10rem;
            background-color: #fff;
            cursor: pointer;
            display: -webkit-box;
            display: flex;
            -webkit-box-align: center;
            align-items: center;
            -webkit-box-pack: justify;
            justify-content: space-between;
          }
        }
        .cmt-textarea {
          width: 100%;
          img {
            cursor: pointer;
            margin-right: 6px;
          }
          textarea {
            resize: none;
          }
        }
        .add-file {
          margin-bottom: -12px;
          margin-left: 10px;
          cursor: pointer;
        }
      }
    }
    .loading-comments {
      height: 200px;
    }
  }
}
