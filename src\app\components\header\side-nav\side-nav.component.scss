@import "../../../../styles/variables";


.sidebar {
  height: 100%;
  width: 75px;
  position: fixed;
  z-index: 1;
  background-color: $theme-white;
  box-shadow: 3px 0px 6px #1919191a;
  font-family: $site-font;
  ul {
    list-style-type: none;
    margin: 0;
    padding: 0;
    margin-top: 70px;
    background-color: $theme-white;
    li a {
      display: block;
      color: #3b3e48;
      padding: 5px;
      text-decoration: none;
    }
    li a:hover:not(.active) {
      background-color: $theme-hover;
      // color: $theme-button;
    }
  }
  .menu-wrapper {
    cursor: pointer;
    .menu-title {
      width: 48px;
      height: 48px;
      border-radius: 10px;
    }
    .menu-text {
      font: normal 600 10px/15px $site-font;
    }
  }

  .support-icon {
    position: absolute;
    bottom: 0px;
    cursor: pointer;
    width: 75px;
  }
  p {
    font: normal normal normal 9px/26px Montserrat;
    font-family: $site-font;
    font-weight: 500;
    text-align: center;
  }
  // review notify alert dot
  .dot {
    height: 5px;
    width: 5px;
    background-color: rgb(248, 0, 0);
    border-radius: 50%;
    margin-top: 10%;
  }
}
