import { AfterViewInit, Component, OnInit, ViewChild } from '@angular/core';
import {
  FormBuilder,
  FormControl,
  FormGroup,
  Validators,
} from '@angular/forms';
import { MatPaginator } from '@angular/material/paginator';
import { MatTableDataSource } from '@angular/material/table';
import { SidePanelService } from '../../services/side-panel.service';
import { Auth0Service } from '../../services/auth0.service';
import { ActivatedRoute, Params, Router } from '@angular/router';
import { UserService } from 'src/app/services/user.service';
// import { AuthService } from '@auth0/auth0-angular';
import { HttpErrorResponse } from '@angular/common/http';
import moment from 'moment';
import { MatSnackBar } from '@angular/material/snack-bar';
import { HomeService } from '../../services/home.service';
import { Subscription, timer } from 'rxjs';
import { FileUploadService } from '../../services/file-upload.service';
import { SnackbarService } from '../../services/snackbar.service';
import { E } from '@angular/cdk/keycodes';
import { ErrorStateMatcher } from '@angular/material/core';

export interface BatchTableList {
  batch_id: string;
  name: string;
  labels: any;
  eta: string;
  total_Rows: number;
  accepted: any;
  others: any;
  actions: number;
}

/** Error when invalid control is dirty, touched */
export class MyErrorStateMatcher implements ErrorStateMatcher {
  isErrorState(control: FormControl | null): boolean {
    return control && control.invalid && (control.dirty || control.touched);
  }
}

@Component({
  selector: 'app-home',

  templateUrl: './home.component.html',
  styleUrls: ['./home.component.scss'],
})
export class HomeComponent implements OnInit {
  uploadBatch: boolean = true;
  batchLog: boolean = false;
  addTag: boolean = false;
  files: File[] = [];
  fileToUpload: File;
  isUpload: boolean = true;
  progress: boolean = false;
  cancelBatch: boolean = false;

  displayedColumns: string[] = [
    'Batch ID',
    'Name & Description',
    'Tags',
    'Created On',
    'ETA',
    'Total Rows',
    'Accepted',
    'Others',
    'Actions',
    'Comments',
  ];
  datePickerOptions: any[];
  currentDate: any;
  customEndDate: Date;
  customStartDate: Date;
  maxDate: Date;
  minDate: Date;
  pageNumber;
  selected = 'recent';
  selectedStatus = '';
  start_date;
  end_date;
  label: string[] = [];
  page: number;
  size: number;
  totalItems: number;
  totalPages: number;
  search: any;
  status: any;
  activeTabName: any;
  SubscriptionID: any;
  //timer
  getBatchListTimer: Subscription;
  batchList: any;
  homeDataSource;
  labelList: any;
  currentBatch: any;
  selectedIndex;
  tableDataLoading: boolean = false;
  dataLoading: boolean = false;
  createlabelFormValues: any;
  uploadBatchFormValues: any;
  uploadProgress: number;
  fileUploadStatus: any = 'Idle';
  uploadButtonLabel: String = 'Upload';
  dropzoneLabel: String = 'Drag and Drop your files here';
  activeChip;
  labelColor: any;
  headercount: any;
  logList;
  logsLoading: boolean = false;
  permissionsObject;
  today;
  tabList: any[] = [
    {
      bucket: 'in_queue',
      description: 'Batches that are undergoing validation prior to processing',
    },
    {
      bucket: 'in_progress',
      description: 'Batches that are undergoing classification',
    },
    {
      bucket: 'processed',
      description:
        'Batches that have been classified. Possible actions: Approve the batch / Download output file / Download input file.',
    },
    {
      bucket: 'approved',
      description:
        'Batches that have been approved post classification. Possible actions: Download output file / Download input file.',
    },
    {
      bucket: 'cancelled',
      description:
        'Batches that could not be processed because of invalid data',
    },
  ];
  uploadInterrupted: boolean = false;
  tabIndexWithData;
  inputTemplates: any[] = [];
  outputTemplates: any[] = [];
  tagCheckRegex = '^(?!.*<[^>]+>).*';

  matcher = new MyErrorStateMatcher();

  constructor(
    private sidepanel: SidePanelService,
    private auth0: Auth0Service,
    private activatedRoute: ActivatedRoute,
    private userService: UserService, // public auth: AuthService
    public matSnackbar: MatSnackBar,
    private homeService: HomeService,
    private fb: FormBuilder,
    public fileUploadService: FileUploadService,
    private router: Router,
    private activatedroute: ActivatedRoute,
    private snackbarService: SnackbarService
  ) {}

  @ViewChild(MatPaginator) paginator: MatPaginator;
  public createLabelForm: FormGroup;
  public uploadBatchForm: FormGroup;
  ngOnInit() {
    this.createLabelForm = this.fb.group({
      name: ['', [Validators.required, Validators.pattern(this.tagCheckRegex), this.noWhitespaceValidator]],
      title: ['', [Validators.required, Validators.pattern(this.tagCheckRegex), this.noWhitespaceValidator]],
    });
    this.uploadBatchForm = this.fb.group({
      name: [
        '',
        [
          Validators.required,
          this.noWhitespaceValidator,
          Validators.maxLength(100),
          Validators.pattern(this.tagCheckRegex),
        ],
      ],
      output_template_id: ['', Validators.required],
      input_template_id: ['', Validators.required],
      description: ['', [Validators.maxLength(2000), this.noWhitespaceValidator, Validators.pattern(this.tagCheckRegex)]],
      reference: ['', [Validators.maxLength(15), this.noWhitespaceValidator, Validators.pattern(this.tagCheckRegex)]],
    });
    this.dataLoading = true;
    //set table data
    this.today = new Date();
    this.search = '';
    this.page = 1;
    this.size = 100;
    this.activeTabName = 'IN_QUEUE';
    this.start_date = '';
    this.end_date = '';
    this.currentDate = moment().format('YYYY-MM-DD');
    const currentyear = new Date().getFullYear();
    this.minDate = new Date(currentyear - 20, 0, 1);
    this.maxDate = new Date();
    this.selected = 'recent';
    this.selectedStatus = '';

    // retrieve subscription id
    this.activatedRoute.queryParams.subscribe((params: Params) => {
      this.SubscriptionID = params.sub;
    });
    // retrieve home page permissionsObject
    this.permissionsObject = this.userService.appPermissions.permissions;
    this.selected = 'recent';
    const currentYear = new Date().getFullYear();
    this.minDate = new Date(currentYear - 20, 0, 1);
    this.maxDate = new Date();
    this.currentDate = moment().format('YYYY-MM-DD');
    this.datePickerOptions = [
      {
        display: 'Recent',
        value: 'recent',
        start_date: '',
      },
      {
        display: 'Last Week',
        value: 'last_week',
        start_date: moment().subtract(7, 'day').format('YYYY-MM-DD'),
      },
      {
        display: 'Last Month',
        value: 'last_month',
        start_date: moment()
          .subtract(1, 'month')
          .add(1, 'day')
          .format('YYYY-MM-DD'),
      },
      {
        display: 'Last Quarter',
        value: 'last_quarter',
        start_date: moment()
          .subtract(3, 'month')
          .add(1, 'day')
          .format('YYYY-MM-DD'),
      },
      {
        value: 'custom_range',
      },
    ];
    this.getCount(
      this.SubscriptionID,
      this.search,
      this.start_date,
      this.end_date
    );
    this.getTable(
      this.page,
      this.size,
      this.activeTabName,
      this.search,
      this.start_date,
      this.end_date,
      this.SubscriptionID
    );
    this.getLabelList(this.SubscriptionID);
  }

  /**
  Custom Validator to restrict whitespace in Input fields
  **/
  public noWhitespaceValidator(control: FormControl) {
    if(control.value) {
    const isWhitespace = (control.value || '').trim().length === 0;
    const isValid = !isWhitespace;
    return isValid ? null : { whitespace: true };
    }
  }

  /**
   * get Date
   * @param range
   * @returns
   */
  getDataForDate = (range: string) => {
    // do nothing for custom range
    if (this.selected == 'custom_range') {
      return null;
    }
    (this.customStartDate = null), (this.customEndDate = null);
    // for others
    let interval = this.datePickerOptions.filter((item) => {
      return item.value === range;
    })[0];
    this.start_date = interval['start_date'];
    if (this.selected == 'recent') {
      this.end_date = '';
    } else {
      this.end_date = this.currentDate;
    }
    this.page = 1;
    this.homeDataSource = [];
    this.getCount(
      this.SubscriptionID,
      this.search,
      this.start_date,
      this.end_date
    );
    this.getTable(
      this.page,
      this.size,
      this.activeTabName,
      this.search,
      this.start_date,
      this.end_date,
      this.SubscriptionID
    );
  };
  sd;
  ed;
  /**
   * set date range
   * @param dateRangeStart
   * @param dateRangeEnd
   */
  dateRangeChange = (dateRangeStart, dateRangeEnd) => {
    if (moment(dateRangeStart).isValid() && moment(dateRangeEnd).isValid()) {
      this.selected = 'custom_range';
      this.sd = moment(dateRangeStart).format('YYYY-MM-DD');
      this.ed = moment(dateRangeEnd).format('YYYY-MM-DD');
      this.page = 1;
      if (this.selected == 'custom_range') {
        this.start_date = this.sd;
        this.end_date = this.ed;
      }
      this.homeDataSource = [];
      this.getCount(
        this.SubscriptionID,
        this.search,
        this.start_date,
        this.end_date
      );
      this.getTable(
        this.page,
        this.size,
        this.activeTabName,
        this.search,
        this.start_date,
        this.end_date,
        this.SubscriptionID
      );
    }
  };

  /**
   * Search based on keyword
   * @param value
   */

  getSearchValue = (value) => {
    this.search = value.trim();
    this.page = 1;
    this.homeDataSource = [];
    this.getCount(
      this.SubscriptionID,
      this.search,
      this.start_date,
      this.end_date
    );
    this.getTable(
      this.page,
      this.size,
      this.activeTabName,
      this.search,
      this.start_date,
      this.end_date,
      this.SubscriptionID
    );
    this.paginator.firstPage();
  };

  resetSearch = () => {
    this.search = '';
    this.homeDataSource = [];
    this.getCount(
      this.SubscriptionID,
      this.search,
      this.start_date,
      this.end_date
    );
    this.getTable(
      this.page,
      this.size,
      this.activeTabName,
      this.search,
      this.start_date,
      this.end_date,
      this.SubscriptionID
    );
  };

  reset = () => {
    this.search = '';
    this.page = 1;
    this.size = 100;
    this.selectedIndex = 0;
    this.start_date = '';
    this.end_date = '';
    this.customStartDate = null;
    this.customEndDate = null;
    this.activeTabName = 'IN_QUEUE';
    this.selected = 'recent';
    this.selectedStatus = '';
    this.homeDataSource = [];
    this.selectedIndex = 0;
    this.getCount(
      this.SubscriptionID,
      this.search,
      this.start_date,
      this.end_date
    );
    this.getTable(
      this.page,
      this.size,
      this.activeTabName,
      this.search,
      this.start_date,
      this.end_date,
      this.SubscriptionID
    );
  };

  /**
   * toggle side panel
   * @param value
   * @param batch_id
   */
  toggleSideNav = (value, batch_id) => {
    this.currentBatch = batch_id;
    if (value == 'uploadBatch') {
      this.batchLog = false;
      this.addTag = false;
      this.uploadBatch = true;
      this.uploadInterrupted = false;
      this.getInputOutputTemplates();
    } else if (value == 'batchLog') {
      this.addTag = false;
      this.uploadBatch = false;
      this.batchLog = true;
    } else {
      this.uploadBatch = false;
      this.batchLog = false;
      this.addTag = true;
    }
    this.sidepanel.setShowNav(true);
  };

  /**
   * close panel
   */
  closeSidepanel = () => {
    this.createLabelForm.reset();
    this.resetTagSelected();
    this.sidepanel.toggleNavState();
    // this.getLabelList(this.SubscriptionID);
    this.files = [];
    this.uploadBatchForm.reset();
  };

  /**
   * get template id
   * @param val file type selected
   */
  getInputOutputTemplates = () => {
    this.homeService.getTemplateId(this.SubscriptionID, 'INPUT').subscribe({
      next: (resp) => {
        this.inputTemplates = resp.result;
        // set default input value on getting data from server
        this.uploadBatchForm.patchValue({
          input_template_id: this.inputTemplates[0].template_id,
        });
      },
      error: (HttpResponse: HttpErrorResponse) => {
        this.snackbarService.openSnackBar(`${HttpResponse.error.detail}`, 'OK');
      },
    });
    this.homeService.getTemplateId(this.SubscriptionID, 'OUTPUT').subscribe({
      next: (resp) => {
        this.outputTemplates = resp.result;
        // set default output value on getting data from server
        this.uploadBatchForm.patchValue({
          output_template_id: this.outputTemplates[0].template_id,
        });
      },
      error: (HttpResponse: HttpErrorResponse) => {
        this.snackbarService.openSnackBar(`${HttpResponse.error.detail}`, 'OK');
      },
    });
  };

  /**
   * on successful copy to clipboard
   */
  showSnackbar = () => {
    this.matSnackbar.open('Copied!', 'OK', {
      duration: 3000,
      horizontalPosition: 'left',
      verticalPosition: 'bottom',
    });
  };

  /**
   *
   * @param event file selection
   */
  onSelect = (event) => {
    if (this.files.length < 1) {
      // console.log(event)
      if (event?.addedFiles[0]?.size == 0) {
        this.showMessage(`File can't be empty`);
      } else {
        this.fileToUpload = event?.addedFiles ? event.addedFiles[0] : undefined;
        event?.addedFiles && this.files.push(...event?.addedFiles);
      }
    } else {
      this.onRemove(event);
    }
    this.isUpload = false;
  };

  /**
   * upload Batch file
   */
  upload = () => {
    this.dropzoneLabel = 'Uploading..';
    this.uploadButtonLabel = 'Uploading..';
    this.fileUploadStatus = 'Uploading';
    // disable form when uploading file
    this.uploadBatchForm.disable();
    // retry upload
    this.uploadInterrupted == true ? this.retryUpload() : this.freshUpload();
  };

  /**
   * fresh batch file upload
   */
  freshUpload = () => {
    this.uploadProgress = 0;
    this.uploadBatchFormValues = this.uploadBatchForm.value;
    this.fileUploadService
      .uploadData(
        this.fileToUpload,
        this.SubscriptionID,
        this.uploadBatchFormValues.name,
        this.uploadBatchFormValues.input_template_id,
        this.uploadBatchFormValues.output_template_id,
        this.uploadBatchFormValues.description,
        this.uploadBatchFormValues.reference
      )
      .subscribe({
        next: (progress) => {
          this.uploadProgress = progress;
          if (this.uploadProgress === 100) {
            // adding timeout of 1s to show progress bar for very small files that uploads instantaneously
            setTimeout(() => {
              this.fileToUpload = undefined;
              this.files = [];
              this.fileUploadStatus = 'Uploaded';
              this.dropzoneLabel = 'Drag and Drop your files here';
              this.uploadButtonLabel = 'Upload';
              this.showMessage('Upload Complete');
              // enable form after file uploaded successfully
              this.uploadBatchForm.enable();
              this.uploadBatchForm.reset();
              this.isUpload = true;
              // upload complete API
              this.fileUploadService.uploadCompelte().subscribe({
                next: (resp) => {},
                complete: () => {
                  this.sidepanel.toggleNavState();
                  this.getTable(
                    this.page,
                    this.size,
                    this.activeTabName,
                    this.search,
                    this.start_date,
                    this.end_date,
                    this.SubscriptionID
                  );
                  this.getCount(
                    this.SubscriptionID,
                    this.search,
                    this.start_date,
                    this.end_date
                  );
                },
                error: (HttpResponse: HttpErrorResponse) => {
                  // prepopulate formats
                  this.uploadBatchForm.patchValue({
                    input_template_id: this.inputTemplates[0].template_id,
                    output_template_id: this.outputTemplates[0].template_id,
                  });
                  this.matSnackbar.open(`${HttpResponse.error.detail}`, 'OK', {
                    duration: 3000,
                  });
                },
              });
            }, 1000);
          }
        },
        error: (error) => {
          // enable form after file uploaded successfully
          this.uploadBatchForm.enable();
          this.uploadBatchForm.reset();
          // prepopulate formats
          this.uploadBatchForm.patchValue({
            input_template_id: this.inputTemplates[0].template_id,
            output_template_id: this.outputTemplates[0].template_id,
          });
          this.fileToUpload = undefined;
          this.files = [];
          this.fileUploadStatus = 'Failed';
          // Retry upload only when file upload was interrupted after a part of it was uploaded
          if (this.uploadProgress > 0) {
            this.uploadButtonLabel = 'Retry';
            this.uploadInterrupted = true;
          }
          this.uploadButtonLabel = 'Upload';
          this.showMessage(JSON.stringify(error.error.detail) || 'Error uploading file, Retry!');
        },
      });
  };

  /**
   * Retry/Resume File Upload Process
   */

  retryUpload = () => {
    this.fileUploadService.resumeFileUpload().subscribe({
      next: (progress) => {
        this.uploadProgress = progress;
        if (this.uploadProgress === 100) {
          setTimeout(() => {
            this.fileToUpload = undefined;
            this.files = [];
            this.fileUploadStatus = 'Uploaded';
            this.dropzoneLabel = 'Drag and Drop your files here';
            this.uploadButtonLabel = 'Upload';
            this.showMessage('Upload Complete');
            // enable form after file uploaded successfully
            this.uploadBatchForm.enable();
            this.uploadBatchForm.reset();
            // call upload complete api
            this.fileUploadService.uploadCompelte().subscribe({
              next: (resp) => {},
              complete: () => {
                this.sidepanel.toggleNavState();
                this.getCount(
                  this.SubscriptionID,
                  this.search,
                  this.start_date,
                  this.end_date
                );
                this.getTable(
                  this.page,
                  this.size,
                  this.activeTabName,
                  this.search,
                  this.start_date,
                  this.end_date,
                  this.SubscriptionID
                );
              },
            });
          }, 1000);
        }
      },
      error: (error: HttpErrorResponse) => {
        // enable form
        this.uploadBatchForm.enable();
        this.fileUploadStatus = 'Failed';
        this.uploadButtonLabel = 'Retry';
        this.closeSidepanel();
        this.showMessage(`Couldn't resume upload, Please try again!`);
      },
    });
  };

  /**
   * Show Toast with message
   * @param msg Message to show
   * @param duration How long to show the toast {defaults to 300}
   */

  showMessage = (msg: string, duration: number = 5000): void => {
    this.matSnackbar
      .open(msg, 'OK', {
        duration,
        horizontalPosition: 'right',
        verticalPosition: 'bottom',
      })
      .onAction()
      .subscribe(() => this.matSnackbar.dismiss());
  };

  /**
   * remove selected file
   * @param event
   */
  onRemove = (event) => {
    this.files.splice(this.files.indexOf(event), 1);
    this.fileToUpload = null;
    this.onSelect(event.hasOwnProperty('addedFiles') ? event : null);
  };

  /**
   * add chip color
   */
  // chipControl = new FormControl(new Set());
  // get chip() {
  //   return this.chipControl.value;
  // }
  // toggleChip = (chip) => {
  //   {
  //     this.chip.add(chip);
  //     this.labelColor = chip;
  //   }
  // };

  /**
   * Tage color
   * @param i
   * @param item
   */
  toggleTagColor = (i, item) => {
    this.labelColor = item;
  };

  resetTagSelected = () => {
    this.labelColor = null;
  };

  /**
   * add label
   */
  addLabel = (addlabel_id, removelabel_id) => {
    this.homeService
      .addLabel(
        this.SubscriptionID,
        this.currentBatch,
        addlabel_id,
        removelabel_id
      )
      .subscribe({
        next: (resp) => {
          this.sidepanel.toggleNavState();
          this.snackbarService.openSnackBar(resp.detail, 'OK');
          this.getTable(
            this.page,
            this.size,
            this.activeTabName,
            this.search,
            this.start_date,
            this.end_date,
            this.SubscriptionID
          );
        },
        error: (HttpResponse: HttpErrorResponse) => {
          this.snackbarService.openSnackBar(
            `${HttpResponse.error.detail}`,
            'OK'
          );
        },
      });
  };

  /**
   * get the table data for respective module
   * @param tabChangeEvent
   */

  tabChanged = (tabChangeEvent): void => {
    this.tableDataLoading = true;
    this.selectedIndex = this.tabList.findIndex(
      (tab) => tab.bucket == tabChangeEvent.tab.textLabel.bucket
    );
    this.activeTabName = tabChangeEvent.tab.textLabel.bucket.toUpperCase();
    this.selectedIndex = tabChangeEvent.index;
    this.homeDataSource = [];
    this.page = 1;
    // refresh count for tabs
    this.homeService
      .getStatusCount(
        this.SubscriptionID,
        this.search,
        this.start_date,
        this.end_date
      )
      .subscribe({
        next: (resp) => {
          this.headercount = resp.result;
        },
        error: () => {},
      });
    this.getTable(
      this.page,
      this.size,
      this.activeTabName,
      this.search,
      this.start_date,
      this.end_date,
      this.SubscriptionID
    );
  };

  /**
   * Remove label for batch
   * @param batch_id
   * @param removelabel_id
   * @param addlabel_id
   */
  removeLabel = (batch_id, removelabel_id, addlabel_id) => {
    this.homeService
      .addLabel(this.SubscriptionID, batch_id, addlabel_id, removelabel_id)
      .subscribe({
        next: (resp) => {
          this.snackbarService.openSnackBar(resp.detail, 'OK');
          this.getTable(
            this.page,
            this.size,
            this.activeTabName,
            this.search,
            this.start_date,
            this.end_date,
            this.SubscriptionID
          );
        },
        error: (HttpResponse: HttpErrorResponse) => {
          this.snackbarService.openSnackBar(
            `${HttpResponse.error.detail}`,
            'OK'
          );
        },
      });
  };

  /**
   * get batch list Table
   * @param page
   * @param size
   * @param status
   * @param search
   * @param start_date
   * @param end_date
   * @param label
   * @param subscription_id
   */
  getTable = (
    page,
    size,
    status,
    search,
    start_date,
    end_date,
    subscription_id
  ) => {
    this.homeService
      .getBatchList(
        page,
        size,
        status,
        search,
        start_date,
        end_date,
        subscription_id
      )
      .subscribe({
        next: (resp) => {
          this.batchList = resp.result;
          const HOME_DATA: BatchTableList[] = this.batchList;

          this.homeDataSource = new MatTableDataSource<BatchTableList>(
            HOME_DATA
          );
          // this.getLabelList(subscription_id);
          this.page = resp.page;
          this.size = resp.page_size;
          this.totalItems = resp.total_items;
          this.totalPages = resp.total_pages;
          this.tableDataLoading = false;
          this.dataLoading = false;
        },
        error: (HttpResponse: HttpErrorResponse) => {
          this.tableDataLoading = false;
          this.matSnackbar.open(`${HttpResponse.statusText}`, 'OK', {
            duration: 3000,
          });
        },
      });
  };

  /**
   * get list of labels
   * @param module_slug
   */
  getLabelList = (subscription_id) => {
    this.homeService.getLabelList(subscription_id).subscribe({
      next: (resp) => {
        this.labelList = resp.result;
      },
      error: (HttpResponse: HttpErrorResponse) => {
        this.matSnackbar.open(`${HttpResponse.error.detail}`, 'OK', {
          duration: 3000,
        });
      },
    });
  };

  /**
   * get list of labels
   * @param module_slug
   */
  getCount = (subscription_id, search, start_date, end_date) => {
    this.homeService
      .getStatusCount(subscription_id, search, start_date, end_date)
      .subscribe({
        next: (resp) => {
          this.headercount = resp.result;
          // go to the first tab which has the data
          this.tabIndexWithData = this.tabList.findIndex(
            (tab) => this.headercount[tab.bucket] > 0
          );
          this.selectedIndex = this.tabIndexWithData;
          this.activeTabName =
            this.tabList[this.tabIndexWithData].bucket.toUpperCase();
          this.getTable(
            this.page,
            this.size,
            this.activeTabName,
            this.search,
            this.start_date,
            this.end_date,
            this.SubscriptionID
          );
        },
        error: (HttpResponse: HttpErrorResponse) => {
          this.snackbarService.openSnackBar(
            `${HttpResponse.error.detail}`,
            'OK'
          );
        },
      });
  };

  /**
   * update batch status
   * @param batch_id,status
   */
  updatebatchStatus = (batch_id, status) => {
    this.homeService
      .statusUpdate(this.SubscriptionID, batch_id, status)
      .subscribe({
        next: (resp) => {
          // this.labelList = resp.result;
          this.snackbarService.openSnackBar(resp.detail, 'OK');
          // get count
          this.homeService
            .getStatusCount(
              this.SubscriptionID,
              this.search,
              this.start_date,
              this.end_date
            )
            .subscribe({
              next: (resp) => {
                this.headercount = resp.result;
                this.selectedIndex = this.tabList.findIndex(
                  (tab) => tab.bucket.toUpperCase() == status
                );
                this.activeTabName = status.toUpperCase();
              },
            });
            // console.log("count")
          // this.getTable(
          //   this.page,
          //   this.size,
          //   this.activeTabName,
          //   this.search,
          //   this.start_date,
          //   this.end_date,
          //   this.SubscriptionID
          // );
        },
        error: (HttpResponse: HttpErrorResponse) => {
          this.snackbarService.openSnackBar(
            `${HttpResponse.error.detail}`,
            'OK'
          );
        },
      });
  };

  /**
   * detele batch
   * @param module_slug
   */
  deleteBatch = (batch_id) => {
    this.homeService.deleteBatchList(this.SubscriptionID, batch_id).subscribe({
      next: (resp) => {
        // this.labelList = resp.result;
        this.matSnackbar.open(resp.detail, 'OK', {
          duration: 3000,
        });
        this.getTable(
          this.page,
          this.size,
          this.activeTabName,
          this.search,
          this.start_date,
          this.end_date,
          this.SubscriptionID
        );
      },
      error: (HttpResponse: HttpErrorResponse) => {
        this.matSnackbar.open(`${HttpResponse.error.detail}`, 'OK', {
          duration: 3000,
        });
      },
    });
  };

  /**
   * Approve or cancel batch
   * @param id
   * @param module_slug
   * @param statusStr
   */
  approveORcancel = (id, statusStr) => {
    this.homeService
      .approveORcancel(this.SubscriptionID, id, statusStr)
      .subscribe({
        next: (resp) => {
          this.snackbarService.openSnackBar(resp.detail, 'OK');
          this.getCount(
            this.SubscriptionID,
            this.search,
            this.start_date,
            this.end_date
          );
          // this.getTable(
          //   this.page,
          //   this.size,
          //   this.activeTabName,
          //   this.search,
          //   this.start_date,
          //   this.end_date,
          //   this.SubscriptionID
          // );
        },
        error: (HttpResponse: HttpErrorResponse) => {
          this.snackbarService.openSnackBar(
            `${HttpResponse.error.detail}`,
            'OK'
          );
        },
      });
  };

  /**
   * update batch status
   * @param module_slug
   */
  createLabel = () => {
    this.createlabelFormValues = this.createLabelForm.value;
    this.homeService
      .createLabel(
        this.SubscriptionID,
        this.createlabelFormValues.name,
        this.createlabelFormValues.title,
        this.labelColor
      )
      .subscribe({
        next: (resp) => {
          this.labelList = resp.result;
          this.createLabelForm.reset();
          this.resetTagSelected();
          this.snackbarService.openSnackBar(resp.detail, 'OK');
          this.getLabelList(this.SubscriptionID);
        },
        error: (HttpResponse: HttpErrorResponse) => {
          // console.log(HttpErrorResponse)
          this.snackbarService.openSnackBar(
            `${JSON.stringify(HttpResponse.error.detail)}`,
            'OK'
          );
        },
      });
  };

  /**
   * generate output file
   * @param batch_id
   */
  generateOutputFile = (batch_id, bucket_type) => {
    this.homeService
      .generateOutputFile(this.SubscriptionID, batch_id, bucket_type)
      .subscribe({
        next: (resp) => {
          this.getTable(
            this.page,
            this.size,
            this.activeTabName,
            this.search,
            this.start_date,
            this.end_date,
            this.SubscriptionID
          );
          this.snackbarService.openSnackBar(resp.detail, 'OK');
        },
        error: (HttpResponse: HttpErrorResponse) => {
          this.snackbarService.openSnackBar(
            `${HttpResponse.error.detail}`,
            'OK'
          );
        },
      });
  };

  /**
   * download batch output file
   * @param batch_id
   */
  downloadFile = (batch_id, bucket_type) => {
    this.homeService.downloadFile(this.SubscriptionID, batch_id, bucket_type).subscribe({
      next: (resp) => {
        window.open(resp.url, '_blank');
        // this.matSnackbar.open(resp.detail, 'OK', {
        //   duration: 3000,
        // });
      },
      error: (HttpResponse: HttpErrorResponse) => {
        this.matSnackbar.open(`${HttpResponse.error.detail}`, 'OK', {
          duration: 3000,
        });
      },
    });
  };

  /**
   * Asset download
   * @param url
   */
  downloadAsset = (url) => {
    window.open(url, '_blank');
  };

  downloadOutputFile = (batch_id, bucket_type) => {
    // this.activeTabName = 'PROCESSED';
    this.getListForOutputFile(this.SubscriptionID, batch_id, bucket_type);
  };

  /**
   * get List For OutputFile
   * @param page
   * @param size
   * @param status
   * @param search
   * @param start_date
   * @param end_date
   * @param subscription_id
   */
  toolTipMessage;
  getListForOutputFile = (subscription_id, batch_id, bucket_type) => {
    this.homeService
      .getSingleBatchList(subscription_id, batch_id, bucket_type)
      .subscribe({
        next: (resp) => {
          this.tableDataLoading = false;
          this.dataLoading = false;
          const lastEdited = resp.last_edited_at;
          const lastGenerated = resp.last_generated_at;
          const download = resp.download;
          const batch_id = resp.batch_id;
          if (!download || download.url === null) {
            this.generateOutputFile(batch_id, bucket_type);
          } else if (download?.in_progress === true && download?.percent_completed !== null) {
            this.toolTipMessage = 'Processing... please wait';
          } else if (download.url == null) {
            this.getTable(
              this.page,
              this.size,
              this.activeTabName,
              this.search,
              this.start_date,
              this.end_date,
              this.SubscriptionID
            );
            this.matSnackbar.open('Processing... please wait', 'OK', { duration: 2000 });
          } else if (
            download.percent_completed === 100 &&
            new Date(lastEdited).getTime() > new Date(lastGenerated).getTime()
          ) {
            this.generateOutputFile(batch_id, bucket_type);
          } else if (
            download.percent_completed === 100 &&
            new Date(lastEdited).getTime() < new Date(lastGenerated).getTime()
          ) {
            this.downloadFile(batch_id, bucket_type);
          }
        },
        error: (HttpResponse: HttpErrorResponse) => {
          this.tableDataLoading = false;
          this.snackbarService.openSnackBar(`${HttpResponse.statusText}`, 'OK');
        },
      });
  };

  // getListForOutputFile = (subscription_id, batch_id, bucket_type) => {
  //    // this.dataload = true;-
  //   this.homeService.getSingleBatchList(subscription_id, batch_id, bucket_type).subscribe({
  //     next: (resp) => {
  //       const lastEdited = resp.last_edited_at;
  //       const lastGenerated = resp.last_generated_at;
  //       const download = resp.download;
  //       if (download?.in_progress === true && download?.percent_completed !== null) {
  //         this.toolTipMessage = 'Processing... please wait.';
  //       } else {
  //         this.toolTipMessage =
  //           download?.percent_completed !== null
  //             ? `${download?.message} (${download?.percent_completed}%)`
  //             : download?.message;
  //       }

  //       if (!download) {
  //         console.log('Calling generateOutputFile with:', batch_id, bucket_type);
  //         this.generateOutputFile(batch_id, bucket_type);
  //       } else if (download.url == null) {
  //         this.getTable(
  //           this.page,
  //           this.size,
  //           this.activeTabName,
  //           this.search,
  //           this.start_date,
  //           this.end_date,
  //           this.SubscriptionID
  //         );
  //         this.matSnackbar.open('Processing... please wait', 'OK', { duration: 3000 });
  //       } else if (
  //         download.url &&
  //         download.percent_completed === 100 &&
  //         new Date(lastEdited).getTime() > new Date(lastGenerated).getTime()
  //       ) {
  //         console.log('Regenerating output file:', batch_id, bucket_type);
  //         this.generateOutputFile(batch_id, bucket_type);
  //       } else if (
  //         download.url &&
  //         download.percent_completed === 100 &&
  //         new Date(lastEdited).getTime() < new Date(lastGenerated).getTime()
  //       ) {
  //         console.log('Downloading file with bucket_type:', bucket_type);
  //         this.downloadFile(batch_id, bucket_type);
  //       } else {
  //         console.error('Unexpected case encountered.');
  //       }
  //     },
  //     error: (HttpResponse: HttpErrorResponse) => {
  //       this.tableDataLoading = false;
  //       this.matSnackbar.open(`${HttpResponse.statusText}`, 'OK', { duration: 3000 });
  //     },
  //   });
  // };

  getLog = (category, category_id) => {
    this.logsLoading = true;
    this.homeService
      .getLog(this.SubscriptionID, category, category_id)
      .subscribe({
        next: (res) => {
          this.logsLoading = false;
          this.logList = res;
        },
        error: (HttpResponse: HttpErrorResponse) => {
          this.logsLoading = false;
          this.snackbarService.openSnackBar(
            `${HttpResponse.error.detail}`,
            'OK'
          );
        },
      });
  };

  /**
   * Get table data on pagination
   * @param identifier
   * @param tabName
   */
  onPaginateChange = (identifier) => {
    this.page = identifier.pageIndex + 1;
    this.size = identifier.pageSize;
    this.getTable(
      this.page,
      this.size,
      this.activeTabName,
      this.search,
      this.start_date,
      this.end_date,
      this.SubscriptionID
    );
  };

  /**
   * Update ETA date
   * @param batch
   * @param ev
   */
  modifyEta = (batch, ev) => {
    this.homeService
      .modifyETA(
        this.SubscriptionID,
        batch,
        new Date(ev).toISOString().split('.')[0] + 'Z'
      )
      .subscribe({
        next: (res) => {
          this.logsLoading = false;
          this.matSnackbar.open(res.detail, 'OK', {
            duration: 3000,
          });
          this.getTable(
            this.page,
            this.size,
            this.activeTabName,
            this.search,
            this.start_date,
            this.end_date,
            this.SubscriptionID
          );
        },
        error: (HttpResponse: HttpErrorResponse) => {
          this.logsLoading = false;
          this.snackbarService.openSnackBar(
            `${HttpResponse.error.detail}`,
            'OK'
          );
        },
      });
  };
}
