@import "../styles/variables";
.app-wrapper {
  flex-direction: column;
  height: auto;
  min-height: 100vh;
  align-items: center;
  justify-content: center;
  background-color: $theme-background;
  .component-wrapper {
    margin-left: 76px;
    margin-top: 70px;
  }
  .content {
    height: 100vh;
  }
  .resolutionDevice {
    padding: 10%;
    margin: auto;
    h1 {
      display: inline-block;
      // text-shadow: 2px 2px #FF0000;
      text-align: center;
      font-family: $site-font;
      font-size: 30px;
      @media screen and (max-width: 600px) {
        font-size: 18px;
      }
      .not-supported-icon {
        vertical-align: middle;
        margin-right: 30px;
        font-size: 40px;
        margin-top: -20px;
      }
    }
  }
}
