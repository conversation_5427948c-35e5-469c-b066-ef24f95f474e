import { Component, OnInit } from '@angular/core';
import { SnackbarService } from '../../services/snackbar.service';

@Component({
  selector: 'app-snackbar',
  templateUrl: './snackbar.component.html',
  styleUrls: ['./snackbar.component.scss']
})
export class SnackbarComponent implements OnInit {

  constructor(private snackBService:SnackbarService) { }

  ngOnInit(): void {
  }
   
  trigger(message:string, action:string)
  {
   this.snackBService.openSnackBar(message, action);
  }

}
