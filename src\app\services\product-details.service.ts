import { Injectable } from '@angular/core';
import { HttpHeaders, HttpClient, HttpParams } from '@angular/common/http';
import { Globals } from '../_globals/endpoints.global';
import { Observable, throwError } from 'rxjs';
import { catchError, map } from 'rxjs/operators';

@Injectable({
  providedIn: 'root',
})
export class ProductDetailsService {
  private httpOptions: HttpHeaders;
  // private endpoints: any = ENDPOINTS;
  constructor(private globals: Globals, private http: HttpClient) {
    this.httpOptions = new HttpHeaders({
      'Content-Type': 'application/json',
    });
  }

  /**
   * Product details
   * @param subscription_id
   * @param row_id
   * @param bucket
   * @param search_queries
   * @param start_date
   * @param end_date
   * @returns
   */
  getProductDetails = (
    subscription_id,
    row_id,
    bucket,
    search_queries,
    product_list,
    start_date,
    end_date
  ): Observable<any> => {
    const productDetailsEndpoint = this.globals.urlJoin(
      'product_details',
      'prediction_list'
    );
    const options = {
      params: new HttpParams()
        .set('start_date', start_date || '')
        .set('end_date', end_date || '')
        .set('bucket', bucket || ''),
    };
    if (product_list && product_list.length > 0) {
      product_list.forEach((item) => {
        options.params = options.params.append('product_list', item);
      });
    }
    if (search_queries && search_queries?.length > 0) {
      search_queries.forEach((item) => {
        options.params = options.params.append('q', item);
      });
    }
    return this.http
      .get(
        productDetailsEndpoint +
          subscription_id +
          '/inputs/' +
          row_id +
          '/predictions',
        options
      )
      .pipe(
        map((response: any) => {
          return response;
        }),
        catchError((error) => throwError(error))
      );
  };

  /**
   * get review row index
   */
  getAttributeList = (subscription_id): Observable<any> => {
    const AttributeListEndpoint = this.globals.urlJoin(
      'product_details',
      'attributeList'
    );
    return this.http
      .get(AttributeListEndpoint + subscription_id + '/attribute_groups')
      .pipe(
        map((response: any) => {
          return response;
        }),
        catchError((error) => throwError(error))
      );
  };

  patchEditedResponse = (subscription_id, row_id, obj): Observable<any> => {
    const editEndpoint = this.globals.urlJoin(
      'product_details',
      'editPredictions'
    );
    return this.http
      .patch(
        editEndpoint +
          subscription_id +
          '/inputs/' +
          row_id +
          '/predictions/edit',
        {
          predictions: obj,
        }
        // {
        //   headers: this.httpOptions,
        //   reportProgress: true,
        //   observe: 'events',
        // }
      )
      .pipe(
        map((response: any) => {
          return response;
        }),
        catchError((error) => throwError(error))
      );
  };
}
