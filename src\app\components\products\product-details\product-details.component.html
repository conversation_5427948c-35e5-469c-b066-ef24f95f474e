<!-- progress spinner -->
<div class="loading-spinner" fxLayoutAlign="center center" *ngIf="tableDataLoading">
  <mat-spinner fxLayoutAlign="center center" diameter="60" strokeWidth="3"></mat-spinner>
</div>

<div class="wrapper" fxFlex="100" fxLayout="column" *ngIf="!tableDataLoading">
  <!-- top toolbar -->
  <div class="filter-container" style="height: 80px">
    <div class="filter-head" fxLayoutAlign="space-between center" fxLayout="row">
      <div fxLayout="row" fxLayoutGap="20px" fxLayoutAlign="space-between center">
        <div class="back-icon" routerLink="/{{ previousPageUrl }}"
          [queryParams]="{ sub: subscriptionId, bucket: bucketVal }">
          <mat-icon>west</mat-icon>
        </div>
        <div class="product-header" fxLayoutAlign="space-between center">
          <span [matTooltip]="productTitle && productTitle.length > 60 ? productTitle : ''">{{ productTitle | truncate: 60 }}</span>
          <!-- <img src="../../../../assets/images/products-page/export.svg" /> -->
        </div>
      </div>

      <!-- next previous btn  -->
      <div class="action-btn" fxLayoutGap="10px" fxLayout="row" fxLayoutAlign="space-between center">
        <button mat-button class="update-btn" (click)="updateFieldsOnClick()">
          Update
        </button>
        <button mat-button class="previous-btn" (click)="previousPage(previousRowId)">
          <mat-icon matPrefix>west</mat-icon>
          Prev
        </button>
        <button mat-button class="previous-btn" (click)="nextPage(nextRowId)">
          <mat-icon matPrefix>east</mat-icon>
          Next
        </button>
        <div class="view-messages-icon" matTooltip="Comments" matTooltipPosition="above" [routerLink]="'/comments'"
        [queryParams]="{sub: subscriptionId, origin: '/product-details', row_id: row_id, bucket: bucket }">
        <!-- <div class="dot"></div> -->
        <img src="assets/images/message.svg" />
      </div>

        <mat-slide-toggle class="text-theme-primary" [checked]="productModeChecked" (change)="toggleSwitches($event)">
          Product Details
        </mat-slide-toggle>
      </div>
    </div>
  </div>

  <!-- product details info card -->
  <div fxLayout="column" class="table-wrapper" style="margin-right: 40px;" fxFlex="100">
    <mat-card class="product-info-container" fxLayoutAlign="space-between start" fxLayout="row">
      <div class="product-image" fxLayout="row">
        <div class="prod-img" fxLayout="column" *ngIf="productImg?.length > 0" (click)="openImageSlider(productImg)">
          <span class="overlay">
            <img fxLayout src="{{ productImg[0].value }}" class="prod-img" />
            <h3>
              <span fxLayout class="image-count">
                {{ productImg.length }}
                <mat-icon style="margin-left: 3px">image</mat-icon>
              </span>
            </h3>
          </span>
        </div>
        <div class="prod-ids" fxLayoutGap="100px" fxLayout="row">
          <div class="product-details" fxLayout="column"  fxLayoutGap="30px">
            <span><span class="bold">Row ID :</span> {{ productIds?.row_id }}</span>
            <span>
              <span class="bold">Batch ID :</span> {{ productIds?.batch_id }}
            </span>
            <div class="tag active" [ngStyle]="
                productBucket == 'rework' && { 'background-color': 'red' }
              ">
              <p>{{ productBucket }}</p>
            </div>
          </div>
          <div fxLayout="column" class="product-details-row"  fxLayoutGap="30px">
            <span *ngFor="let item of productDetailsData.classification_attributes"><span class="bold">{{item.name}} :</span> {{item.values}}</span>
          </div>
        </div>
      </div>
      <!-- bucket update action -->
      <div class="move_to_frmField" fxLayout="row" *ngIf="moveToBucketList && moveToBucketList.length > 0">
        <mat-form-field appearance="none">
          <mat-select placeholder="Move To">
            <mat-option *ngFor="let bucket of moveToBucketList" (click)="updateBucket(rowId, bucket)">
              {{ bucket | titlecase }}
            </mat-option>
          </mat-select>
        </mat-form-field>
      </div>
    </mat-card>
    <!-- details tab  -->
    <mat-tab-group class="tab-container vertical-tab-container">
      <mat-tab [label]="tabHeader.name" *ngFor="let tabHeader of tabList">
        <mat-card class="product-field" fxLayout="row">
          <!-- no data section -->
          <!-- <div fxFlex="60"
            *ngIf="
              !productDetailsData[tabHeader.slug] ||
              productDetailsData[tabHeader.slug].length == 0"
            fxLayoutAlign="center center"
            class="no-data"
          >
            <mat-icon fontSet="material-icons-outlined">info</mat-icon>&nbsp;
            <span class="no-pred-found"> No Data available </span>
          </div> -->

          <div fxLayout="column" fxFlex="60">
            <div class="tab-attr" fxLayout="column" *ngFor="
                let attribute of productDetailsData[tabHeader.slug];
                let i = index
              ">
              <!-- for text data type -->
              <div class="mat-form-field mat-form-field-appearance-outline" fxFlex="100"
                *ngIf="attribute.data_type === 'TEXT'">
                <mat-form-field appearance="outline" [ngClass]="{ edit: attribute.needs_editing }" floatLabel="always"
                  fxFlex="81">
                  <mat-label>{{ attribute.name }}</mat-label>
                  <input matInput fxFlex="95" value="{{ attribute.values }}" [(ngModel)]="attribute.values"
                    [readonly]="!attribute.is_editable" (change)="
                      onChangeOfInput(attribute.prediction_id, attribute.values)
                    " />
                  <mat-icon *ngIf="!attribute.is_editable" class="lock-icon" matTooltip="Read only"
                    matTooltipPosition="above">lock</mat-icon>
                </mat-form-field>
                <!-- <mat-icon fxFlex="10" fxFlex.lt-md="1" fxFlex.lt-sm="1" *ngIf="attribute.existing !== null"
                  class="existing-icon" matTooltip="{{
                    attribute?.existing.toString() | commmaSeperator
                  }}" [matTooltipPosition]="'above'">info
                </mat-icon> -->
              </div>

              <!-- for AUTOCOMPLETE_SINGLE_SELECT data type -->
              <div class="mat-form-field mat-form-field-appearance-outline" [ngClass]="
                  attribute.prediction_id &&
                  attribute.prediction_id === prediction_id
                    ? 'mat-focused'
                    : false
                " fxFlex="100" *ngIf="attribute.data_type === 'AUTOCOMPLETE_SINGLE_SELECT'">
                <mat-form-field appearance="outline" [ngClass]="{ edit: attribute.needs_editing }" floatLabel="always"
                  fxFlex="81">
                  <mat-label>{{ attribute.name }}</mat-label>
                  <input type="text" value="{{ attribute.values }}" [(ngModel)]="attribute.values"
                    [readonly]="!attribute.is_editable" matInput [formControl]="myControl" [matAutocomplete]="auto"
                    (ngModelChange)="autocompleteAttrSlug = attribute.slug" />
                  <mat-spinner matSuffix diameter="20" *ngIf="optionsLoading"></mat-spinner>
                  <mat-autocomplete #auto="matAutocomplete" (optionSelected)="
                      onChangeOfInput(
                        attribute.prediction_id,
                        $event.option.value
                      )
                    ">
                    <mat-option *ngFor="let option of filteredOptions | async" [value]="option">
                      {{ option }}
                    </mat-option>
                    <mat-option disabled *ngIf="
                        autoCompleteSuggestions?.length === 0 && !optionsLoading
                      ">
                      No Data found
                    </mat-option>
                  </mat-autocomplete>
                </mat-form-field>
                <mat-icon fxFlex="10" fxFlex.lt-md="1" fxFlex.lt-sm="1" *ngIf="attribute.existing !== null"
                  class="existing-icon" matTooltip="{{
                    attribute.existing.toString() | commmaSeperator
                  }}" [matTooltipPosition]="'above'">info
                </mat-icon>
              </div>

              <!-- for ASSET_LINK data type -->
              <div class="mat-form-field mat-form-field-appearance-outline" [ngClass]="
                  attribute.prediction_id &&
                  attribute.prediction_id === prediction_id
                    ? 'mat-focused'
                    : false
                " fxFlex="100" *ngIf="attribute.data_type === 'ASSET_LINK'">
                <mat-form-field appearance="outline" [ngClass]="{ edit: attribute.needs_editing }" floatLabel="always"
                  fxFlex="81">
                  <mat-label>{{ attribute.name }}</mat-label>
                  <input matInput fxFlex="95" value="{{ attribute.values }}" [(ngModel)]="attribute.values"
                    [readonly]="!attribute.is_editable" (change)="
                      onChangeOfInput(attribute.prediction_id, attribute.values)
                    " />
                  <a class="mapped-asset" *ngIf="attribute.mapped_asset && attribute.mapped_asset != ''"
                    href="{{ attribute.mapped_asset }}" target="_blank">
                    <mat-icon>link</mat-icon>
                  </a>
                  <mat-icon *ngIf="!attribute.is_editable" class="lock-icon" matTooltip="Read only"
                    matTooltipPosition="above">lock</mat-icon>
                </mat-form-field>
                <mat-icon fxFlex="10" fxFlex.lt-md="1" fxFlex.lt-sm="1" *ngIf="attribute.existing !== null"
                  class="existing-icon" matTooltip="{{
                    attribute.existing.toString() | commmaSeperator
                  }}" [matTooltipPosition]="'above'">info
                </mat-icon>
              </div>

              <!-- for data type Link -->
              <div class="assets-div" *ngIf="attribute.data_type === 'LINK'" fxFlex="100">
                <mat-label class="label">{{ attribute.name }} : </mat-label>
                <a [href]="item" target="_blank" *ngFor="let item of attribute.values">
                  {{ item }}
                </a>
              </div>

              <div class="mat-form-field mat-form-field-appearance-outline" [ngClass]="
                  attribute.prediction_id &&
                  attribute.prediction_id === prediction_id
                    ? 'mat-focused'
                    : false
                " fxFlex="100" *ngIf="attribute.data_type === 'CLOSED_LIST_SINGLE_SELECT_STATIC'">
                <!-- For type CLOSED_LIST_SINGLE_SELECT_STATIC-->
                <mat-form-field appearance="outline" [ngClass]="{ edit: attribute.needs_editing }" fxFlex="81" *ngIf="
                    attribute.data_type === 'CLOSED_LIST_SINGLE_SELECT_STATIC'
                  ">
                  <mat-label>{{ attribute.name }} closed list </mat-label>
                  <mat-select [(value)]="attribute.values[0]" (selectionChange)="
                      onChangeOfInput(
                        attribute.prediction_id,
                        attribute.values[0]
                      )
                    ">
                    <mat-option *ngFor="let choices of attribute.choices" [value]="choices">
                      {{ choices }}
                    </mat-option>
                  </mat-select>
                </mat-form-field>
                <mat-icon fxFlex="10" fxFlex.lt-md="1" fxFlex.lt-sm="1" *ngIf="attribute.existing !== null"
                  class="existing-icon" matTooltip="{{
                    attribute.existing.toString() | commmaSeperator
                  }}" [matTooltipPosition]="'above'">info
                </mat-icon>
              </div>
            </div>
          </div>

          <!-- <div class="loading-comments"
          fxLayoutAlign="center center"
          *ngIf="commentsLoading && commentThread.length == 0"
          fxFlex="100"
        >
          <mat-spinner
            fxLayoutAlign="center center"
            diameter="40"
            strokeWidth="3"
          ></mat-spinner>
        </div> -->
          <!-- comment thread scroll container -->
          <div class="product-detail-comments" fxFlex="30" #scrollContainer fxLayout="column" fxLayoutGap="20px"
            fxLayoutAlign="start center" infiniteScroll [scrollWindow]="false" [infiniteScrollDistance]="1"
            [infiniteScrollUpDistance]="1" [infiniteScrollThrottle]="50" (scrolled)="onCommentThreadScroll()">
            <!-- comment area -->
            <div class="comment-box" fxLayout="row">
              <div fxLayout="row" class="cmt-textarea" fxLayoutAlign="space-between center">
                <!-- text area -->
                <textarea matInput placeholder="Comment or add others with @" [(ngModel)]=comments[activeCommentIndex]
                  #textareaRef></textarea>
                <!-- tag configuration -->
                <flx-mentions [textInputElement]="textareaRef" [menuTemplate]="tagTemplate" [triggerCharacter]="'@'"
                  [getChoiceLabel]="getChoiceLabel" (search)="loadChoices($event)"
                  (selectedChoicesChange)="onSelectedChoicesChange($event)">
                </flx-mentions>
                <!-- tag template -->
                <ng-template #tagTemplate let-selectChoice="selectChoice">
                  <ul class="flx-selectable-list">
                    <li *ngFor="let user of choices" class="flx-selectable-list-item" (click)="selectChoice(user)">
                      <span title="{{user.name}}">{{user.name}}</span>
                    </li>
                  </ul>
                </ng-template>
                <!-- post comment -->
                <img fxLayout="column" class="send-icon" src="../../../assets/images/review-page/comment-icon.svg"
                  (click)="postComment(comments[activeCommentIndex], activeCommentIndex); " />
              </div>
            </div>
            <!-- comments scroll -->
            <div fxLayout="row" class="cmt-profile" *ngFor="
            let comment of commentThread; let i=index;
          " fxLayoutAlign="center start">
              <!-- comment profile picture -->
              <img src="{{ comment.profile_picture }}" />
              <div fxLayout="column" class="cmt-profile-picture">
                <!-- comment header -->
                <div fxLayout="row" class="comment-head" fxLayoutAlign="space-between center">
                  <div fxLayout="column">
                    <p class="username">{{ comment.created_by }}</p>
                    <p class="comment-time">{{ comment.created_at | date: 'EEEE, MMMM d, y' }}</p>
                  </div>
                  <div *ngIf="userData.username == comment.created_by_username">
                    <img src="../../../assets/images/review-page/more.svg" [matMenuTriggerFor]="menu" />
                    <mat-menu #menu="matMenu">
                      <button (click)="editComment(comment); comment.editable = true;" mat-menu-item>Edit</button>
                      <button (click)="deleteComment(comment.comment_id, i)" mat-menu-item>Delete</button>
                    </mat-menu>
                  </div>

                </div>

                <!-- comment content -->
                <div class="comments" fxLayoutAlign="space-between center" fxFlex="100">
                  <span style="width: 80%" *ngIf="!comment.editable"><span class="tagged-users text-theme-primary"
                      *ngFor="let user of comment.tagged_users">@{{user.name}}</span> {{comment.text}}</span>
                </div>
                <!-- edit comment part -->
                <div class="edit-comment-box" fxLayout="row" *ngIf="comment.editable"
                  fxLayoutAlign="space-between center">
                  <!-- text area -->
                  <textarea matInput name="message" class="edit-comment-text" [(ngModel)]=comment.edited_text
                    #message='ngModel' #editCommentRef></textarea>
                  <!-- tag configuration -->
                  <flx-mentions [textInputElement]="editCommentRef" [menuTemplate]="menuTemplate"
                    [triggerCharacter]="'@'" [selectedChoices]="getSelectedChoices(comment)"
                    [getChoiceLabel]="getChoiceLabel" (search)="loadChoices($event)"
                    (choiceRemoved)="taggedUserRemovedInCommentEdit($event)"
                    (selectedChoicesChange)="onTaggedUserEdited($event, comment)">
                  </flx-mentions>
                  <!-- tag template-->
                  <ng-template #menuTemplate let-selectChoice="selectChoice">
                    <ul class="flx-selectable-list">
                      <li *ngFor="let user of choices" class="flx-selectable-list-item" (click)="selectChoice(user)">
                        <span title="{{user.name}}">{{user.name}}</span>
                      </li>
                    </ul>
                  </ng-template>
                  <!-- update and cancel -->
                  <div fxLayout="row" fxLayoutGap="5px" fxLayoutAlign="space-between center">
                    <img fxLayout="column" class="send-icon" src="../../../assets/images/review-page/comment-icon.svg"
                      (click)="updateComment(comment)" />
                    <mat-icon (click)="cancelEdit(comment);">cancel</mat-icon>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </mat-card>
      </mat-tab>
    </mat-tab-group>
  </div>
</div>

