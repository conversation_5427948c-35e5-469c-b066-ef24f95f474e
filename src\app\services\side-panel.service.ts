import { Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs';
import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { Globals } from '../_globals/endpoints.global';
import { Observable, throwError } from 'rxjs';
import { map, catchError } from 'rxjs/operators';
import { ENDPOINTS } from '../_globals/endpoints';

@Injectable({
  providedIn: 'root',
})

export class SidePanelService {
  private showNav$: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(
    false
  );
  constructor(private globals: Globals, private http: HttpClient) {}

  showNavStatus = this.showNav$.asObservable();

  getShowNav() {
    return this.showNav$.asObservable();
  }

  /**
   * show / hide panel
   */
  setShowNav(showHide: boolean) {
    this.showNav$.next(showHide);
  }

  toggleNavState() {
    this.showNav$.next(!this.showNav$.value);
  }

  isNavOpen() {
    return this.showNav$.value;
  }
}
