// This file can be replaced during build by using the `fileReplacements` array.
// `ng build` replaces `environment.ts` with `environment.prod.ts`.
// The list of file replacements can be found in `angular.json`.

export const environment = {
  production: false,
  auth0: {
    domain: 'auth-dev.datax.ai',
    clientId: '8OkodSrnTqu1oM9truGoNikMGSJmymbF',
    callbackURL: 'https://r2a.app-local.datax.ai:4500/loading',
    useRefreshTokens: true,
    audience: 'https://*.datax.ai/api/modules/list',
  },
  default_return_url: 'https://app.login-local.datax.ai',
};
/*
 * For easier debugging in development mode, you can import the following file
 * to ignore zone related error stack frames such as `zone.run`, `zoneDelegate.invokeTask`.
 *
 * This import should be commented out in production mode because it will have a negative impact
 * on performance if an error is thrown.
 */
// import 'zone.js/plugins/zone-error';  // Included with Angular CLI.
