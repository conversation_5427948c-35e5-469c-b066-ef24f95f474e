import { DataSource } from '@angular/cdk/collections';
import { HttpErrorResponse } from '@angular/common/http';
import { Component, OnInit, Inject } from '@angular/core';
import {
  MatSnackBarRef,
  MAT_SNACK_BAR_DATA,
  MatSnackBar,
} from '@angular/material/snack-bar';
import { UndoService } from '../../services/undo.service';


@Component({
  selector: 'app-undobulk-snackbar',
  templateUrl: './undobulk-snackbar.component.html',
  styleUrls: ['./undobulk-snackbar.component.scss'],
})
export class UndoBulkSnackbarComponent implements OnInit {
  response: any;
  sub_id: any;
  row_id: any;
  loading: boolean;
  row_id_list: any;
  previousBucket: any;

  constructor(
    public snackBarRef: MatSnackBarRef<UndoBulkSnackbarComponent>,
    @Inject(MAT_SNACK_BAR_DATA)
    public data: any,
    private undoService: UndoService,
    private matSnackbar: MatSnackBar,
  ) {}

  ngOnInit() {
    this.response = this.data.response;
    this.sub_id = this.data.subscriptionId;
    this.row_id_list = this.data.row_id_list;
    this.previousBucket = this.data.previousBucket;
  }

  bulkUndo = () => {
    this.loading = true;
    this.response = 'PROCESSING..';
    let undoObj = {
      row_id_list: this.row_id_list,
    };
    this.undoService.bulkUndo(
      undoObj,
      this.sub_id
    ).subscribe({
      next: (resp) => {
        this.loading = false;
        console.log('this is a bulkundo function in side  ');
        this.matSnackbar.open('Undo Successful!', 'OK', { duration: 3000 });
        this.data.onUndo();
      },
      error: (HttpResponse: HttpErrorResponse) => {
        this.loading = false;
        this.matSnackbar.open(`Undo failed: ${HttpResponse.error.detail}`, 'OK', { duration: 3000 });
      },
    });
  };
}

