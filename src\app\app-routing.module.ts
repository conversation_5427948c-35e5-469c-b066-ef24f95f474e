import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { HomeComponent } from './components/home/<USER>';
import { ProductsComponent } from './components/products/products.component';
import { CommentsComponent } from './components/comments/comments.component';
import { LoadingComponent } from './components/loading/loading.component';
import { HelpComponent } from './components/help/help.component';
import { AuthGuard } from '@auth0/auth0-angular';
import { AppAuthGuard } from './_guards/auth.guard';
import { OfflineComponent } from './components/offline/offline.component';
const routes: Routes = [
  {
    path: 'home',
    component: HomeComponent,
    data: { title: ' - Home' },
    canActivate: [AppAuthGuard],
  },
  {
    path: 'loading',
    component: LoadingComponent,
    data: { title: ' - Loading' },
    canActivate: [AuthGuard],
  },
  {
    path: 'products',
    component: ProductsComponent,
    data: { title: ' - Products' },
    canActivate: [AuthGuard, AppAuthGuard],
  },
  {
    path: 'comments',
    component: CommentsComponent,
    data: { title: ' - Comments' },
    canActivate: [AuthGuard, AppAuthGuard],
  },
  {
    path: 'offline',
    component: OfflineComponent,
    data: { title: ' - offline' },
    // canActivate: [AuthGuard, AppAuthGuard],
  },
  {
    path: 'help',
    component: HelpComponent,
    data: { title: ' - Help' },
    canActivate: [AuthGuard, AppAuthGuard],
  },
  {
    path: '**',
    component: HomeComponent,
    canActivate: [AuthGuard, AppAuthGuard],
  },
];

@NgModule({
  imports: [RouterModule.forRoot(routes)],
  exports: [RouterModule],
})
export class AppRoutingModule {}
