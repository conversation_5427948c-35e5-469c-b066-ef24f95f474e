import { Component, Input, OnInit } from '@angular/core';
import { SidePanelService } from '../../services/side-panel.service';
import { Observable } from 'rxjs';
import { HttpErrorResponse, HttpResponse } from '@angular/common/http';

export enum SideNavDirection {
  Left = 'left',
  Right = 'right',
}
@Component({
  selector: 'app-side-panel',
  templateUrl: './side-panel.component.html',
  styleUrls: ['./side-panel.component.scss'],
})
export class SidePanelComponent implements OnInit {
  showSideNav: Observable<boolean>;

  @Input() sidenavTemplateRef: any;
  @Input() duration: number = 0.25;
  @Input() navWidth: number = window.innerWidth;
  @Input() direction: SideNavDirection = SideNavDirection.Left;

  constructor(private sidepanel: SidePanelService) {}

  ngOnInit(): void {
    this.showSideNav = this.sidepanel.getShowNav();
  }

  // close panel
  onSidebarClose() {
    this.sidepanel.setShowNav(false);
  }
  // panel direction / show
  getSideNavBarStyle(showNav: boolean) {
    let navBarStyle: any = {};

    navBarStyle.transition =
      this.direction +
      ' ' +
      this.duration +
      's, visibility ' +
      this.duration +
      's';
    navBarStyle.width = this.navWidth + 'px';
    navBarStyle[this.direction] = (showNav ? 0 : this.navWidth * -1) + 'px';

    return navBarStyle;
  }
}
