import { Component, OnInit } from '@angular/core';
import { COMMA, ENTER } from '@angular/cdk/keycodes';
import { MatChipInputEvent } from '@angular/material/chips';
import { MatDialog } from '@angular/material/dialog';
import { ImageViewerDialogComponent } from 'src/app/_dialogs/image-viewer-dialog/image-viewer-dialog.component';
import moment from 'moment';
import { Router, ActivatedRoute } from '@angular/router';
import { ReviewService } from '../../services/review.service';
import { HttpErrorResponse } from '@angular/common/http';
import { MatSnackBar } from '@angular/material/snack-bar';
import { ReferenceUrlDialogComponent } from '../../_dialogs/reference-url-dialog/reference-url-dialog.component';
import { ChoiceWithIndices } from '@flxng/mentions';
import { CommentsService } from 'src/app/services/comments.service';
import { UndoSnackbarComponent } from '../../_dialogs/undo-snackbar/undo-snackbar.component';

export interface Fruit {
  name: string;
}
interface User {
  name: string;
  username: string;
}
@Component({
  selector: 'app-review',
  templateUrl: './review.component.html',
  styleUrls: ['./review.component.scss'],
})
export class ReviewComponent implements OnInit {
  visible = true;
  selectable = true;
  removable = true;
  addOnBlur = true;
  readonly separatorKeysCodes: number[] = [ENTER, COMMA];
  fruits: Fruit[] = [];
  datePickerOptions: any[];
  currentDate: any;
  customEndDate: string;
  customStartDate: string;
  maxDate: Date;
  minDate: Date;
  pageNumber;
  selected = 'recent';
  start_date;
  end_date;
  page: number;
  size: number;
  selectedProducts = {};
  subscriptionId: any;
  filterList: any;
  searchedMultipleVals: string[] = [];
  filterObj: Object = {};
  datePickerValue;
  searchedItems: string[] = [];
  reviewPageFilters;
  batch_id;
  search: string;
  dataLoading;
  reviewList;
  previousProductId;
  nextProductId;
  reviewData: any[] = [];
  currentItem;
  totalItems;
  rowId;
  predictionId;
  predictionName;
  // comments
  activeCommentIndex = 1;
  comments: any[] = [];
  text = ``;
  loading = false;
  choices: User[] = [];
  mentions: ChoiceWithIndices[] = [];
  mentionUsers: any[];
  taggedUsersByUsername: any[] = [];
  taggedUsersByName: any[] = [];
  taggedUsers: any[] = [];
  defSuggestionDisplayCount: number = 3;
  commentThread: any[] = [];
  threadPage: number = 1;
  threadSize: number = 10;
  threadTotalItems: number;
  threadTotalPage: number;
  commentsLoading: boolean;
  commentsListForType = 'row';
  userData;
  searchInComment = '';
  nextid;
  selectedFilters: string[] = [];
  productId;
  diableBtn;
  diableNextBtn;

  constructor(
    private reviewService: ReviewService,
    public dialog: MatDialog,
    private router: Router,
    private activatedroute: ActivatedRoute,
    public matSnackbar: MatSnackBar,
    private commentsService: CommentsService
  ) {}

  ngOnInit() {
    // add subscription id from local storage in url as param
    this.subscriptionId = localStorage.getItem('SubscriptionID');
    this.getReviewFilterList(this.subscriptionId);
    this.currentDate = moment().format('YYYY-MM-DD');
    const currentYear = new Date().getFullYear();
    this.minDate = new Date(currentYear - 20, 0, 1);
    this.maxDate = new Date();
    // get user data from local storage
    if (localStorage.getItem('user')) {
      this.userData = JSON.parse(localStorage.getItem('user'));
    }
    // get all previous filters from local storage only if current module is same as module in localstorage item reviewFilterObj
    if (localStorage.getItem('reviewFilterObj'))
      this.reviewPageFilters = JSON.parse(
        localStorage.getItem('reviewFilterObj')
      );
    if (
      this.reviewPageFilters &&
      this.reviewPageFilters != 'undefined' &&
      JSON.parse(localStorage.reviewFilterObj).hasOwnProperty(
        this.subscriptionId
      )
    ) {
      // retrieve all filter values
      this.reviewPageFilters[this.subscriptionId].q.forEach((element) => {
        this.searchedItems.push(element);
      });
      this.datePickerValue = this.reviewPageFilters[this.subscriptionId]
        ?.date_picker_value
        ? this.reviewPageFilters[this.subscriptionId].date_picker_value
        : 'recent';
      this.selected = this.datePickerValue;
      this.selectedProducts = this.reviewPageFilters[this.subscriptionId]
        ?.product_list_model
        ? this.reviewPageFilters[this.subscriptionId].product_list_model
        : {};
      this.start_date = this.reviewPageFilters[this.subscriptionId]?.start_date
        ? this.reviewPageFilters[this.subscriptionId]?.start_date
        : '';
      this.end_date = this.reviewPageFilters[this.subscriptionId]?.end_date
        ? this.reviewPageFilters[this.subscriptionId]?.end_date
        : '';
      this.searchedMultipleVals = this.reviewPageFilters[this.subscriptionId]
        ?.product_list
        ? this.reviewPageFilters[this.subscriptionId].product_list
        : [];
      //  this.selectedProducts = this.reviewPageFilters? this.reviewPageFilters[this.subscriptionId].product_list : [];
    } else {
      this.start_date = '';
      this.end_date = '';
      this.searchedMultipleVals = [];
      this.searchedItems = [];
    }

    // push batch Id from URL in searched items array
    // ignore whitespaces
    if (
      this.batch_id &&
      this.batch_id != '' &&
      this.searchedItems.indexOf(this.batch_id.trim()) < 0
    ) {
      this.searchedItems.push(this.batch_id.trim());
    }
    // create an object to be saved in local storage
    this.filterObj[this.subscriptionId] = {
      q: this.searchedItems,
      date_picker_value: this.datePickerValue,
      start_date: this.start_date,
      end_date: this.end_date,
      product_list_model: this.selectedProducts,
      product_list: this.searchedMultipleVals,
    };
    localStorage.setItem('reviewFilterObj', JSON.stringify(this.filterObj));

    this.datePickerOptions = [
      {
        display: 'Recent',
        value: 'recent',
        start_date: '',
      },
      {
        display: 'Last Week',
        value: 'last_week',
        start_date: moment().subtract(7, 'day').format('YYYY-MM-DD'),
      },
      {
        display: 'Last Month',
        value: 'last_month',
        start_date: moment()
          .subtract(1, 'month')
          .add(1, 'day')
          .format('YYYY-MM-DD'),
      },
      {
        display: 'Last Quarter',
        value: 'last_quarter',
        start_date: moment()
          .subtract(3, 'month')
          .add(1, 'day')
          .format('YYYY-MM-DD'),
      },
      {
        display: 'Custom range',
        value: 'custom_range',
      },
    ];
    // get review product details
    this.getReviewProductDetail(
      this.searchedItems,
      this.searchedMultipleVals,
      this.start_date,
      this.end_date
    );
    // get user name List
    this.getUserNameList();
  }

  /**
   * get options for user list dropdown
   * @param searchTerm
   * @returns
   */
  async loadChoices(searchTerm: string): Promise<User[]> {
    const users = await this.mentionUsers;
    this.choices = users.filter((user) => {
      const alreadyExists = this.mentions.some(
        (m) => m.choice.name === user.name
      );
      return (
        !alreadyExists &&
        user.name.toLowerCase().indexOf(searchTerm.toLowerCase()) > -1
      );
    });
    return this.choices;
  }

  /**
   * tag display format
   * @param user
   * @returns
   */
  getChoiceLabel = (user: User): string => {
    return `@${user.name}`;
  };

  /**
   * tag selection changes
   * @param choices
   */
  onSelectedChoicesChange(choices: ChoiceWithIndices[]): void {
    this.mentions = choices;
    this.taggedUsers = choices.map((selection) => selection.choice);
    this.taggedUsersByUsername = this.taggedUsers.map((user) => user.username);
    this.taggedUsersByName = this.taggedUsers.map((user) => user.name);
  }

  /**
   * tag selection changes
   * @param choices
   */
  onTaggedUserEdited(choices: ChoiceWithIndices[], comment): void {
    this.mentions = choices;
    comment.edited_tagged_users = choices.map((selection) => selection.choice);
  }

  /**
   * get Date
   * @param range
   * @returns
   */
  getDataForDate = (range: string) => {
    // do nothing for custom range
    if (this.selected == 'custom_range') {
      return null;
    }
    (this.customStartDate = ''), (this.customEndDate = '');
    // for others
    let interval = this.datePickerOptions.filter((item) => {
      return item.value === range;
    })[0];
    this.start_date = interval['start_date'];
    if (this.selected == 'recent') {
      this.end_date = '';
    } else {
      this.end_date = this.currentDate;
    }
    this.page = 1;
    this.size = 5;
    // retain datepicker dropdown value for auto selection
    this.datePickerValue = range;
    // update reviewFilterObj in localstorage
    this.filterObj[this.subscriptionId] = {
      q: this.searchedItems,
      start_date: this.start_date,
      end_date: this.end_date,
      product_list: this.searchedMultipleVals,
      product_list_model: this.selectedProducts,
      date_picker_value: this.datePickerValue,
    };
    localStorage.setItem('reviewFilterObj', JSON.stringify(this.filterObj));
    this.getReviewProductDetail(
      this.searchedItems,
      this.searchedMultipleVals,
      this.start_date,
      this.end_date
    );
  };

  sd;
  ed;
  /**
   * set date range
   * @param dateRangeStart
   * @param dateRangeEnd
   */
  dateRangeChange = (
    dateRangeStart: HTMLInputElement,
    dateRangeEnd: HTMLInputElement
  ) => {
    if (
      moment(dateRangeStart?.value, 'DD-MM-YYYY').isValid() &&
      moment(dateRangeEnd?.value, 'DD-MM-YYYY').isValid()
    ) {
      this.selected = 'custom_range';
      this.sd = moment(dateRangeStart?.value, 'DD-MM-YYYY').format(
        'YYYY-MM-DD'
      );
      this.ed = moment(dateRangeEnd?.value, 'DD-MM-YYYY').format('YYYY-MM-DD');
      this.page = 1;
      this.size = 5;
      if (this.selected == 'custom_range') {
        this.start_date = this.sd;
        this.end_date = this.ed;
      }
    }
    this.getReviewProductDetail(
      this.searchedItems,
      this.searchedMultipleVals,
      this.start_date,
      this.end_date
    );
  };
  /**
   * search chip
   * @param event
   */
  addQuery(event: MatChipInputEvent): void {
    const value = event.value;
    if (value && value != '' && this.searchedItems.indexOf(value.trim()) < 0) {
      this.searchedItems.push(value.trim());
      this.filterObj[this.subscriptionId] = {
        q: this.searchedItems,
        start_date: this.start_date,
        end_date: this.end_date,
        product_list_model: this.selectedProducts,
        product_list: this.searchedMultipleVals,
        date_picker_value: this.datePickerValue,
      };
      localStorage.setItem('reviewFilterObj', JSON.stringify(this.filterObj));
      this.getReviewProductDetail(
        this.searchedItems,
        this.searchedMultipleVals,
        this.start_date,
        this.end_date
      );
      this.search = '';
    }
  }

  /**
   * Removes search query from filter list
   * @param item
   */
  removeQuery = (item) => {
    const index = this.searchedItems.indexOf(item.trim());
    this.searchedItems.splice(index, 1);
    this.filterObj[this.subscriptionId] = {
      q: this.searchedItems,
      start_date: this.start_date,
      end_date: this.end_date,
      product_list_model: this.selectedProducts,
      product_list: this.searchedMultipleVals,
      date_picker_value: this.datePickerValue,
    };
    localStorage.setItem('reviewFilterObj', JSON.stringify(this.filterObj));
    this.getReviewProductDetail(
      this.searchedItems,
      this.searchedMultipleVals,
      this.start_date,
      this.end_date
    );
  };

  /**
   * reset filter
   */
  resetFilters = () => {
    this.search = '';
    this.selected = 'recent';
    this.datePickerValue = 'recent';
    this.searchedItems = [];
    this.searchedMultipleVals = [];
    this.filterObj = {};
    this.start_date = '';
    this.end_date = '';
    this.selectedProducts = {};
    this.router.navigate([], {
      relativeTo: this.activatedroute,
      queryParams: { sub: this.subscriptionId },
    });
    localStorage.removeItem('reviewFilterObj');
    this.customStartDate = '';
    this.customEndDate = '';
    this.getReviewFilterList(this.subscriptionId);
    this.filterList = [];
    this.getReviewProductDetail(
      this.searchedItems,
      this.searchedMultipleVals,
      this.start_date,
      this.end_date
    );
  };

  resetSearch = () => {
    this.search = '';
    this.getReviewProductDetail(
      this.searchedItems,
      this.searchedMultipleVals,
      this.start_date,
      this.end_date
    );
  };

  /**
   * get filters on selection
   */
  getProductSelection = () => {
    this.searchedMultipleVals = [];
    this.selectedProducts;
    for (const property in this.selectedProducts) {
      if (this.selectedProducts[property].length) {
        this.selectedProducts[property].forEach((element) => {
          this.searchedMultipleVals.push(element);
        });
      }
    }
    this.filterObj[this.subscriptionId] = {
      q: this.searchedItems,
      start_date: this.start_date,
      end_date: this.end_date,
      product_list_model: this.selectedProducts,
      product_list: this.searchedMultipleVals,
      date_picker_value: this.datePickerValue,
    };
    localStorage.setItem('reviewFilterObj', JSON.stringify(this.filterObj));
    this.getReviewProductDetail(
      this.searchedItems,
      this.searchedMultipleVals,
      this.start_date,
      this.end_date
    );
  };

  /**
   * get options for filters
   * @param subscription_id
   */
  getReviewFilterList = (subscription_id) => {
    this.reviewService.getReviewFilterList(subscription_id, true).subscribe({
      next: (resp) => {
        this.filterList = resp.result;
      },
      error: (HttpResponse: HttpErrorResponse) => {
        this.matSnackbar.open(`${HttpResponse.error.detail}`, 'OK', {
          duration: 3000,
        });
      },
    });
  };

  /**
   * get row index id
   * @param subscription_id
   * @param row_index
   */
  getRowIndex = (subscription_id, row_index) => {
    this.reviewService.getRowIndex(subscription_id, row_index).subscribe({
      next: (resp) => {},
      error: (HttpResponse: HttpErrorResponse) => {
        this.matSnackbar.open(`${HttpResponse.error.detail}`, 'OK', {
          duration: 3000,
        });
      },
    });
  };

  openImageSlider = (productImg) => {
    this.dialog.open(ImageViewerDialogComponent, {
      data: { productImg },
    });
  };

  /**
   * get review product data
   * @param search
   * @param filter
   * @param start_date
   * @param end_date
   */
  getReviewProductDetail = (search, filter, start_date, end_date) => {
    this.dataLoading = true;
    this.reviewService
      .getReviewProductDetail(
        search,
        filter,
        start_date,
        end_date,
        this.subscriptionId
      )
      .subscribe({
        next: (resp) => {
          this.dataLoading = false;
          // this.isDataPresent = resp.result.length === 0 ? true : false;
          this.reviewData = [];
          this.reviewList = resp.result.row_data;
          this.reviewList &&
            Object.keys(this.reviewList).length > 0 &&
            this.reviewData.push(this.reviewList);
          this.previousProductId = resp.result.previous;
          this.nextProductId = resp.result.next;
          // Current and Total items
          this.currentItem = resp.result.current_index;
          this.totalItems = resp.result.total_rows;
          this.reviewData.forEach((data) => {
            data.activeAttribute = data?.predictions[0]?.display_name;
            let count = 0;
            data.main_attribute_count = count;
            // sort so that all items wit main_attribute true comes first
            data.predictions.sort(function (a, b) {
              if (b.main_attribute == true) {
                count++;
              }
              data.main_attribute_count = count;
              return b.main_attribute - a.main_attribute;
            });
          });
          this.predictionId = resp.result[0]?.predictions[0].prediction_id;
          this.predictionName = resp.result[0]?.predictions[0].display_name;
          this.rowId = resp.result.row_data.row_id;
          if (this.rowId) {
            this.getCommentThread(
              this.subscriptionId,
              this.threadPage,
              this.threadSize,
              this.commentsListForType,
              this.rowId,
              this.searchInComment,
              true
            );
          }
          return this.reviewData;
        },
        error: (HttpResponse: HttpErrorResponse) => {
          this.dataLoading = false;
           this.snackbarService.openSnackBar(`${HttpResponse.error.detail}`, 'OK');
        },
      });
  };

  /**
   * Reference Dialog
   * @param value
   */
  openDialogReferenceURL = (value) => {
    this.dialog.open(ReferenceUrlDialogComponent, {
      data: {
        value: value,
      },
    });
  };

  /**
   * View more btn
   * @param index, event
   */
  viewMore = (e, i) => {
    var dots = document.getElementById('dots-' + i);
    var moreText = document.getElementById('more-' + i);
    var btnText = document.getElementById('moreBtn-' + i);

    if (dots.style.display === 'none') {
      dots.style.display = 'inline';
      btnText.innerHTML = 'View More';
      moreText.style.display = 'none';
    } else {
      dots.style.display = 'none';
      btnText.innerHTML = 'View less';
      moreText.style.display = 'inline';
    }
  };

  /**
   * Accept/Reject suggestion
   * @param item
   * @param prediction
   * @param row_id
   * @param suggestion
   * @param val
   * @param is_accepted
   */
  updateSuggestion = (
    item,
    prediction,
    row_id,
    suggestion,
    val,
    is_accepted
  ) => {
    this.dataLoading = true;
    this.nextid = row_id;
    this.reviewService
      .suggestionAccceptDecilne(
        this.subscriptionId,
        row_id,
        prediction.prediction_id,
        suggestion,
        is_accepted
      )
      .subscribe({
        next: (resp) => {
          this.dataLoading = false;
          // if main attribute is rejected, sku directly goes to rework
          // so go to next sku
          if (val == 'reject' && prediction.main_attribute == true) {
            this.nextid = this.nextProductId;
            // Undo snackbar
            let snackBarRef = this.matSnackbar.openFromComponent(
              UndoSnackbarComponent,
              {
                duration: 30000,
                // passing args as data
                data: {
                  response: resp.detail,
                  module: this.subscriptionId,
                  Id: row_id,
                  onUndo: () => {
                    this.getRowData(
                      this.searchedItems,
                      this.selectedFilters,
                      this.start_date,
                      this.end_date,
                      this.subscriptionId,
                      row_id
                    );
                  },
                },
              }
            );
            // call next sku
            this.getRowData(
              this.searchedItems,
              this.searchedMultipleVals,
              this.start_date,
              this.end_date,
              this.subscriptionId,
              this.nextid
            );
          } else {
            // call same sku
            this.getRowData(
              this.searchedItems,
              this.searchedMultipleVals,
              this.start_date,
              this.end_date,
              this.subscriptionId,
              row_id
            );
          }
        },
        error: (HttpResponse: HttpErrorResponse) => {
          this.dataLoading = false;
          // 400 means the value was edited in product detail page
          // so get updated values
          if (HttpResponse.status === 400) {
            this.reviewService
              .updateSuggestions(this.subscriptionId, row_id)
              .subscribe({
                next: (res) => {
                  Object.assign(item, res[0]);
                  item.activeAttribute = res[0]?.predictions[0].display_name;
                },
              });
          }
           this.snackbarService.openSnackBar(`${HttpResponse.error.detail}`, 'OK');
        },
      });
  };

  /**
   * post comment
   * @param comment comment
   * @param i index
   */
  postComment = (comment, i) => {
    let modifiedComment = this.removeAllTagsFromComment(
      comment,
      this.taggedUsersByName
    );
    let obj = {
      comment: modifiedComment.trim(),
      tagged_users: this.taggedUsersByUsername,
    };
    this.commentsService
      .postComment(
        this.subscriptionId,
        this.commentsListForType,
        this.rowId,
        obj
      )
      .subscribe({
        next: (resp) => {
          // work around to remove highlighted tag in textarea
          let elements = document.getElementsByClassName(
            'flx-text-highlight-tag'
          );
          while (elements.length > 0) elements[0].remove();
          this.comments[i] = '';
          this.mentions = [];
          this.taggedUsers = [];
          this.taggedUsersByName = [];
          this.taggedUsersByUsername = [];
           this.snackbarService.openSnackBar(resp.detail, 'OK');
          this.threadTotalItems = this.threadTotalItems + 1;
          this.onCommentThreadScroll();
        },
        error: (HttpResponse: HttpErrorResponse) => {
          this.dataLoading = false;
           this.snackbarService.openSnackBar(`${HttpResponse.error.detail}`, 'OK');
        },
      });
  };

  /**
   * get user list for tagging in comments
   */
  getUserNameList = () => {
    this.commentsService.getUserNamesToTag(this.subscriptionId).subscribe({
      next: (resp) => {
        this.mentionUsers = resp.result;
        // return resp;
      },
    });
  };

  /**
   * get comment thread for respective sku/batch comment
   * @param subs_id
   * @param page
   * @param size
   * @param category
   * @param batch_or_row_id
   * @param q
   * @param comment_thread
   */
  getCommentThread = (
    subs_id,
    page,
    size,
    category,
    batch_or_row_id,
    q,
    comment_thread
  ) => {
    this.commentsLoading = true;
    this.commentsService
      .getCommentsList(
        subs_id,
        page,
        size,
        category,
        batch_or_row_id,
        q,
        comment_thread
      )
      .subscribe({
        next: (resp) => {
          this.commentsLoading = false;
          this.threadPage = resp.page;
          this.threadSize = resp.page_size;
          this.threadTotalItems = resp.total_items;
          this.threadTotalPage = resp.total_pages;
          // if difference between total pages capacity and length of comment thread list is less than one page capacity
          // replace the last set of data with incoming data
          // using splice to replace
          if (
            this.threadPage * this.threadSize - this.commentThread.length <
            this.threadSize
          ) {
            this.commentThread.splice(
              (this.threadPage - 1) * this.threadSize,
              this.commentThread.length -
                (this.threadPage - 1) * this.threadSize,
              ...resp.result
            );
          } else {
            this.commentThread = [...this.commentThread, ...resp.result];
          }
        },
        error: (HttpResponse: HttpErrorResponse) => {
          this.commentsLoading = false;
           this.snackbarService.openSnackBar(`${HttpResponse.error.detail}`, 'OK');
        },
      });
  };

  /**
   * used to highlight existing tagged users in textarea
   * @param comment
   * @returns
   */
  getSelectedChoices = (comment): User[] => {
    return comment.tagged_users;
  };

  /**
   * edit comment
   * @param comment
   */
  editComment = (comment) => {
    // store original values
    comment.edited_text = comment.complete_text;
    comment.edited_tagged_users = comment.tagged_users;
    // deactivate all other comment edits
    this.commentThread
      .filter((item) => item.comment_id != comment.comment_id)
      .forEach((row) => {
        row.editable = false;
      });
  };

  /**
   * cancel comment editing mode
   * @param comment
   */
  cancelEdit = (comment) => {
    comment.editable = false;
  };

  /**
   * delete comment from comment thread
   * @param comment_id
   * @param index
   */
  deleteComment = (comment_id, index) => {
    this.commentsService
      .deleteComment(this.subscriptionId, comment_id)
      .subscribe({
        next: (resp) => {
           this.snackbarService.openSnackBar(resp.detail, 'OK');
          this.commentThread.splice(index, 1);
        },
        error: (HttpResponse: HttpErrorResponse) => {
          this.dataLoading = false;
           this.snackbarService.openSnackBar(`${HttpResponse.error.detail}`, 'OK');
        },
      });
  };

  /**
   * tag removal event in comment thread
   * @param e
   */
  taggedUserRemovedInCommentEdit = (e) => {};

  /**
   * update existing comment
   * @param comment
   */
  updateComment = (comment) => {
    comment.editable = false;
    let taggedUserListByUsername = comment.edited_tagged_users.map(
      (user) => user.username
    );
    let taggedUserListByName = comment.edited_tagged_users.map(
      (user) => user.name
    );
    let modifiedComment = this.removeAllTagsFromComment(
      comment.edited_text,
      taggedUserListByName
    );
    let obj = {
      text: modifiedComment.trim(),
      tagged_users: taggedUserListByUsername,
    };
    this.commentsService
      .updateComment(this.subscriptionId, comment.comment_id, obj)
      .subscribe({
        next: (resp) => {
          this.mentions = [];
           this.snackbarService.openSnackBar(resp.detail, 'OK');
          // update the comment values on client side
          comment.complete_text = comment.edited_text;
          comment.text = modifiedComment.trim();
          comment.tagged_users = comment.edited_tagged_users;
        },
        error: (HttpResponse: HttpErrorResponse) => {
          this.dataLoading = false;
           this.snackbarService.openSnackBar(`${HttpResponse.error.detail}`, 'OK');
        },
      });
  };

  /**
   * to remove all tags from comment text
   * @param str
   * @param mapObj
   * @returns
   */
  removeAllTagsFromComment = (str, mapObj) => {
    var re = new RegExp(mapObj.map((item) => '@' + item).join('|'), 'gi');
    return str.replace(re, '');
  };

  /**
   * event fired when comment thread section is scrolled
   * fetching new pages
   */
  onCommentThreadScroll = () => {
    // check if comment thread length is equal to total item capacity of pages
    // or check if total item size > total item capacity of pages
    // increment pageSize by 1
    if (this.threadTotalItems > this.commentThread.length) {
      if (this.commentThread.length == this.threadSize * this.threadPage) {
        this.threadPage++;
        this.getCommentThread(
          this.subscriptionId,
          this.threadPage,
          this.threadSize,
          this.commentsListForType,
          this.rowId,
          this.searchInComment,
          true
        );
      } else {
        this.getCommentThread(
          this.subscriptionId,
          this.threadPage,
          this.threadSize,
          this.commentsListForType,
          this.rowId,
          this.searchInComment,
          true
        );
      }
    }
  };

  /**
   * Request review btn
   * @param batchId
   * @param bucketValue
   */
  requestRework = (rowId, bucketValue) => {
    let obj = { bucket: bucketValue.toUpperCase() };
    this.rowId = this.nextProductId;
    this.reviewService.bucketUpdate(rowId, obj, this.subscriptionId).subscribe({
      next: (resp) => {
        if (resp['body'] != undefined) {
          this.getRowData(
            this.searchedItems,
            this.selectedFilters,
            this.start_date,
            this.end_date,
            this.subscriptionId,
            this.rowId
          );
          this.matSnackbar.openFromComponent(UndoSnackbarComponent, {
            duration: 30000,
            // passing args as data
            data: {
              response: resp.body.detail,
              module: this.subscriptionId,
              Id: rowId,
              onUndo: () => {
                this.getRowData(
                  this.searchedItems,
                  this.selectedFilters,
                  this.start_date,
                  this.end_date,
                  this.subscriptionId,
                  rowId
                );
              },
            },
          });
        }
      },
      error: (HttpError: HttpErrorResponse) => {
        this.matSnackbar.open(`${HttpError.error.detail}`, 'OK', {
          duration: 3000,
        });
      },
    });
  };

  /**
   * Accept or Decline suggestions (all suggestion)
   * @param row_id,
   *  prediction_id, obj, module_slug,prediction_id,suggestion,is_accepted
   */
  acceptAllSuggestions = (item, row_id, is_accepted) => {
    this.rowId = this.nextProductId;
    this.reviewService
      .suggestionAccceptDecilneAll(this.subscriptionId, row_id, is_accepted)
      .subscribe({
        next: (resp) => {
          this.dataLoading = true;
          // splice entire row object
          let index = this.reviewData.indexOf(item);
          this.reviewData.splice(index, 1);
          this.getRowData(
            this.searchedItems,
            this.selectedFilters,
            this.start_date,
            this.end_date,
            this.subscriptionId,
            this.rowId
          );
          // this.matSnackbar.open(resp.detail, 'OK', {
          //   duration: 3000,
          // });
          this.matSnackbar.openFromComponent(UndoSnackbarComponent, {
            duration: 30000,
            // passing args as data
            data: {
              response: resp.detail,
              module: this.subscriptionId,
              Id: row_id,
              onUndo: () => {
                this.getRowData(
                  this.searchedItems,
                  this.selectedFilters,
                  this.start_date,
                  this.end_date,
                  this.subscriptionId,
                  row_id
                );
              },
            },
          });
        },
        error: (HttpResponse: HttpErrorResponse) => {
           this.snackbarService.openSnackBar(`${HttpResponse.error.detail}`, 'OK');
        },
      });
  };

  /**
   * get a particular row updated data
   * probably during accept/reject
   * @param search
   * @param filter
   * @param start_date
   * @param end_date
   * @param subs_id
   * @param row_id
   * @returns
   */
  getRowData = (search, filter, start_date, end_date, subs_id, row_id) => {
    if (row_id == 'None') {
      this.getReviewProductDetail(
        this.searchedItems,
        this.searchedMultipleVals,
        this.start_date,
        this.end_date
      );
      return;
    }
    this.reviewService
      .getReviewRow(search, filter, start_date, end_date, subs_id, row_id)
      .subscribe({
        next: (resp) => {
          this.reviewData = [];
          this.dataLoading = false;
          // this.isDataPresent = resp.result.length === 0 ? true : false;
          this.reviewList = resp.result.row_data;
          this.reviewData.push(this.reviewList);

          this.previousProductId = resp.result.previous;
          this.nextProductId = resp.result.next;
          // Current and Total items
          this.currentItem = resp.result.current_index;
          this.totalItems = resp.result.total_rows;
          this.reviewData.forEach((data) => {
            data.activeAttribute = data.predictions[0]?.display_name;
            let count = 0;
            data.main_attribute_count = count;
            // sort so that all items wit main_attribute true comes first
            data.predictions.sort(function (a, b) {
              if (b.main_attribute == true) {
                count++;
              }
              data.main_attribute_count = count;
              return b.main_attribute - a.main_attribute;
            });
          });
          this.predictionId = resp.result[0]?.predictions[0].prediction_id;
          this.predictionName = resp.result[0]?.predictions[0].display_name;
          this.rowId = resp.result.row_data.row_id;

          this.getCommentThread(
            this.subscriptionId,
            this.threadPage,
            this.threadSize,
            this.commentsListForType,
            this.rowId,
            this.searchInComment,
            true
          );
          return this.reviewData;
        },
      });
  };

  /**
   * Prev & next product
   * @param val
   * @returns
   */
  changeProduct = (val) => {
    this.dataLoading = true;
    // to check Previous or Next product
    if (val == 'previousProduct') {
      this.productId = this.previousProductId;
    } else {
      this.productId = this.nextProductId;
    }
    // to check none
    if (this.productId == 'None') {
      this.dataLoading = false;
      this.matSnackbar.open('No more Products Available', 'OK', {
        duration: 5000,
      });
      return;
    }
    this.reviewService
      .getReviewRow(
        this.searchedItems,
        this.searchedMultipleVals,
        this.start_date,
        this.end_date,
        this.subscriptionId,
        this.productId
      )
      .subscribe({
        next: (resp) => {
          this.reviewData = [];
          this.dataLoading = false;
          // this.isDataPresent = resp.result.length === 0 ? true : false;
          this.reviewList = resp.result.row_data;
          this.reviewData.push(this.reviewList);
          this.previousProductId = resp.result.previous;
          this.nextProductId = resp.result.next;
          // Current and Total items
          this.currentItem = resp.result.current_index;
          this.totalItems = resp.result.total_rows;

          this.reviewData.forEach((data) => {
            data.activeAttribute = data.predictions[0]?.display_name;
            let count = 0;

            data.main_attribute_count = count;
            // sort so that all items wit main_attribute true comes first
            data.predictions.sort(function (a, b) {
              if (b.main_attribute == true) {
                count++;
              }
              data.main_attribute_count = count;
              return b.main_attribute - a.main_attribute;
            });
          });
          this.predictionId = resp.result[0]?.predictions[0].prediction_id;
          this.predictionName = resp.result[0]?.predictions[0].display_name;
          this.rowId = resp.result.row_data.row_id;

          this.getCommentThread(
            this.subscriptionId,
            this.threadPage,
            this.threadSize,
            this.commentsListForType,
            this.rowId,
            this.searchInComment,
            true
          );
          return this.reviewData;
        },
        error: (HttpResponse: HttpErrorResponse) => {
          this.dataLoading = false;
          // this.errorOccurs = true;
           this.snackbarService.openSnackBar(`${HttpResponse.error.detail}`, 'OK');
        },
      });
  };
}
