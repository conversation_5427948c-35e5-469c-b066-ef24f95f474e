{"name": "module-r2a", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve --port 4500 --public-host r2a.app-local.datax.ai --ssl true", "build": "ng build", "build-dev": "ng build --configuration='dev'", "build-staging": "ng build --configuration='stag'", "build-prod": "ng build --configuration='production'", "watch": "ng build --watch --configuration development", "test": "ng test"}, "private": true, "dependencies": {"@angular-material-components/datetime-picker": "^6.0.3", "@angular/animations": "~12.1.0", "@angular/cdk": "^12.2.13", "@angular/common": "~12.1.0", "@angular/compiler": "~12.1.0", "@angular/core": "~12.1.0", "@angular/flex-layout": "^13.0.0-beta.36", "@angular/forms": "~12.1.0", "@angular/material": "^12.2.13", "@angular/platform-browser": "~12.1.0", "@angular/platform-browser-dynamic": "~12.1.0", "@angular/router": "~12.1.0", "@auth0/auth0-angular": "^1.8.2", "@flxng/mentions": "^1.1.4", "chart.js": "^3.7.0", "moment": "^2.29.1", "ng2-charts": "^3.0.8", "ngx-dropzone": "^3.0.0", "ngx-infinite-scroll": "^10.0.1", "ngx-mat-select-search": "^4.2.0", "ngx-pinch-zoom": "^2.6.2", "rxjs": "~6.6.0", "tslib": "^2.2.0", "webpack-bundle-analyzer": "^4.5.0", "zone.js": "~0.11.4"}, "devDependencies": {"@angular-devkit/build-angular": "~12.1.4", "@angular/cli": "^12.1.0", "@angular/compiler-cli": "~12.1.0", "@types/jasmine": "~3.8.0", "@types/node": "^12.11.1", "jasmine-core": "~3.8.0", "karma": "~6.3.0", "karma-chrome-launcher": "~3.1.0", "karma-coverage": "~2.0.3", "karma-jasmine": "~4.0.0", "karma-jasmine-html-reporter": "~1.7.0", "typescript": "~4.3.2"}}