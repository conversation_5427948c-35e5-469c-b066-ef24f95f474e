import { Component, OnInit } from '@angular/core';
import { MatSnackBar } from '@angular/material/snack-bar';
import { FormGroup, FormBuilder, Validators } from '@angular/forms';
import { HelpService } from 'src/app/services/help.service';
import { HttpErrorResponse } from '@angular/common/http';
import { ActivatedRoute, Params } from '@angular/router';
import { SnackbarService } from '../../services/snackbar.service';

@Component({
  selector: 'app-help',
  templateUrl: './help.component.html',
  styleUrls: ['./help.component.scss'],
})
export class HelpComponent implements OnInit {
  userInfo;
  public helpForm: FormGroup;
  helpFormValues;
  SubscriptionID;

  constructor(
    private matSnackbar: MatSnackBar,
    private fb: FormBuilder,
    private helpService: HelpService,
    private activatedRoute: ActivatedRoute,
    private snackbarService: SnackbarService
  ) {}

  ngOnInit(): void {
    this.helpForm = this.fb.group({
      email: ['', Validators.required],
      feedback: ['', Validators.required],
    });
    // retrieve subscription id
    this.activatedRoute.queryParams.subscribe((params: Params) => {
      this.SubscriptionID = params.sub;
    });
    // me api response from local storage
    this.userInfo =
      (localStorage.getItem('user') &&
        JSON.parse(localStorage.getItem('user'))) ||
      undefined;
    // this.getUserDetails();
    this.helpForm.controls['email'].setValue(this.userInfo.email);
  }

  /**
   * post feedback
   */
  helpFormSubmit = () => {
    this.helpFormValues = this.helpForm.value;
    this.helpService
      .postFeedback(
        this.helpFormValues.email,
        this.helpFormValues.feedback,
        this.SubscriptionID
      )
      .subscribe({
        next: (resp) => {
           this.snackbarService.openSnackBar(resp.detail, 'OK');
          this.resetForm();
          this.helpForm.controls['email'].setValue(this.userInfo.email);
        },
        error: (HttpResponse: HttpErrorResponse) => {
           this.snackbarService.openSnackBar(`${HttpResponse.error.detail}`, 'OK');
        },
      });
  };

  /**
   * reset form
   */
  resetForm = () => {
    this.helpForm.reset();
    this.helpForm.controls['email'].setValue(this.userInfo.email);
  };
}
