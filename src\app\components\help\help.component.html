<div class="help-wrapper" fxLayout="column" fxLayoutGap="30px">
  <!-- component header -->
  <form [formGroup]="helpForm">
    <div class="help-head" fxLayout="column" fxLayoutAlign="start start" fxLayoutGap="20px">
      <div class="help-page-client-logo" *ngIf="userInfo?.client_logo">
        <img src="{{ userInfo.client_logo }}" />
      </div>
    </div>

    <div class="help-body">
      <div fxLayout="column" fxFlex="50">
        <div fxLayout="column">
          <span class="form-field-heading">Email</span>
          <mat-form-field appearance="outline">
            <input matInput placeholder="<EMAIL>" formControlName="email" readonly/>
          </mat-form-field>
        </div>

        <div fxLayout="column">
          <span class="form-field-heading">Your feedback</span>
          <mat-form-field appearance="outline">
            <textarea matInput placeholder="Type your text here…" formControlName="feedback"></textarea>
          </mat-form-field>
        </div>

        <div class="btn-group" fxLayout="row" fxLayoutGap="10px" fxFlex="100">
          <button style="text-align: center" class="theme-black-stroked-btn" mat-stroked-button (click)="resetForm()">
            Cancel
          </button>

          <button class="theme-blue-raised-btn save" color="primary" mat-raised-button type="submit"
            [disabled]="helpForm.invalid" (click)="helpFormSubmit()">
            Save
          </button>
        </div>
      </div>
    </div>
  </form>
</div>
