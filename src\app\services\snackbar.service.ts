import { Injectable } from '@angular/core';
import { MatSnackBar, MatSnackBarConfig } from '@angular/material/snack-bar';

@Injectable({
  providedIn: 'root',
})
export class SnackbarService {
  constructor(private snackBar: MatSnackBar) {}

  openSnackBar(message: any, action: string) {
    if (message === 'undefined' || message === undefined) {
      this.snackBar.open(
        `An unexpected error has occurred. Please contact dataX support.`,
        action,
        {
          duration: 3000,
        }
      );
    } else {
      this.snackBar.open(message, action, {
        duration: 3000,
      });
    }
  }
  // openSnackBar(message: any, action: string, duration: number = 3000) {
  //   const config = new MatSnackBarConfig();
  //   config.duration = duration;

  //   if (message === 'undefined' || message === undefined) {
  //     this.snackBar.open(
  //       `An unexpected error has occurred. Please contact dataX support.`,
  //       action,
  //       config
  //     );
  //   } else {
  //     this.snackBar.open(message, action, config);
  //   }
  // }
}
