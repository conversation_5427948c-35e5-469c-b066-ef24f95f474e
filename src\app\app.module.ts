import { NgModule } from '@angular/core';
import { BrowserModule } from '@angular/platform-browser';
import { CommonModule } from '@angular/common';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
//angular material
import { MaterialModule } from '../material';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { NgxDropzoneModule } from 'ngx-dropzone';
import { NgChartsModule } from 'ng2-charts';
import { PinchZoomModule } from 'ngx-pinch-zoom';
import { NgxMatSelectSearchModule } from 'ngx-mat-select-search';

//http imports
import {
  HttpClientModule,
  HTTP_INTERCEPTORS,
  HttpClientXsrfModule,
} from '@angular/common/http';
import { HttpConfigInterceptor } from './_interceptors/http-config.interceptor';
import { MAT_DATE_LOCALE } from '@angular/material/core';
import { HomeComponent } from './components/home/<USER>';
//pipe
import { UnderscoreAsSpacePipe } from './_pipe/underscore-as-space.pipe';
import { TruncatePipe } from './_pipe/truncate.pipe';
import { SafeContentPipe } from './_pipe/safe-content.pipe';
import { CommmaSeperatorPipe } from './_pipe/commma-seperator.pipe';
import { HighlightWordPipe } from './_pipe/highlight-word.pipe';

//components
import { AppRoutingModule } from './app-routing.module';
import { AppComponent } from './app.component';
import { TopNavComponent } from './components/header/top-nav/top-nav.component';
import { SideNavComponent } from './components/header/side-nav/side-nav.component';
import { SidePanelComponent } from './components/side-panel/side-panel.component';
import { ProductsComponent } from './components/products/products.component';
// import { ReviewComponent } from './components/review/review.component';
// import { SettingsComponent } from './components/settings/settings.component';
// import { ProductDetailsComponent } from './components/products/product-details/product-details.component';
import { CommentsComponent } from './components/comments/comments.component';
import { ImageViewerDialogComponent } from './_dialogs/image-viewer-dialog/image-viewer-dialog.component';
import { AuthHttpInterceptor, AuthModule } from '@auth0/auth0-angular';
import { environment } from '../environments/environment';
import { LoadingComponent } from './components/loading/loading.component';
import { MentionsModule } from '@flxng/mentions';
import { UndoSnackbarComponent } from './_dialogs/undo-snackbar/undo-snackbar.component';
import { InfiniteScrollModule } from 'ngx-infinite-scroll';
import { HelpComponent } from './components/help/help.component';
import { ReferenceUrlDialogComponent } from './_dialogs/reference-url-dialog/reference-url-dialog.component';
// import { ReviewModeComponent } from './components/products/product-details/review-mode/review-mode.component';
import { OfflineComponent } from './components/offline/offline.component';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatInputModule } from '@angular/material/input';
import {
  NgxMatDatetimePickerModule,
  NgxMatNativeDateModule,
  NgxMatTimepickerModule,
} from '@angular-material-components/datetime-picker';
import { SnackbarComponent } from './components/snackbar/snackbar.component';
import { FormatTooltipPipe } from './_pipe/format-tooltip.pipe';
import { ClickOutsideDirective } from './_directive/click-outside.directive';
import { UndoBulkSnackbarComponent } from './_dialogs/undobulk-snackbar/undobulk-snackbar.component';

@NgModule({
  declarations: [
    AppComponent,
    HomeComponent,
    UnderscoreAsSpacePipe,
    TruncatePipe,
    SafeContentPipe,
    CommmaSeperatorPipe,
    TopNavComponent,
    SideNavComponent,
    SidePanelComponent,
    ProductsComponent,
    CommentsComponent,
    ImageViewerDialogComponent,
    LoadingComponent,
    UndoSnackbarComponent,
    HelpComponent,
    ReferenceUrlDialogComponent,
    HighlightWordPipe,
    OfflineComponent,
    SnackbarComponent,
    FormatTooltipPipe,
    ClickOutsideDirective,
    UndoBulkSnackbarComponent
  ],
  imports: [
    HttpClientModule,
    BrowserModule,
    CommonModule,
    AppRoutingModule,
    BrowserAnimationsModule,
    MaterialModule,
    FormsModule,
    ReactiveFormsModule,
    NgxDropzoneModule,
    PinchZoomModule,
    NgChartsModule,
    MentionsModule,
    InfiniteScrollModule,
    MatDatepickerModule,
    MatInputModule,
    NgxMatDatetimePickerModule,
    NgxMatTimepickerModule,
    NgxMatNativeDateModule,
    NgxMatSelectSearchModule,
    AuthModule.forRoot({
      domain: environment.auth0.domain,
      clientId: environment.auth0.clientId,
      redirectUri: window.location.origin + '/loading',
      useRefreshTokens: environment.auth0.useRefreshTokens,
      audience: environment.auth0.audience,
      prompt: 'none',
      httpInterceptor: {
        allowedList: ['/api/*'],
      },
    }),
    HttpClientXsrfModule.withOptions({
      cookieName: 'csrftoken',
      headerName: 'x-csrf',
    }),
  ],
  providers: [
    {
      provide: HTTP_INTERCEPTORS,
      useClass: AuthHttpInterceptor,
      multi: true,
    },
    {
      provide: HTTP_INTERCEPTORS,
      useClass: HttpConfigInterceptor,
      multi: true,
    },
    // AppPermissionsService,
    { provide: MAT_DATE_LOCALE, useValue: 'en-GB' },
  ],
  bootstrap: [AppComponent],
})
export class AppModule {}
