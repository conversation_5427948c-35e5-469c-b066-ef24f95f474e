<div class="wrapper" fxFlex="100" fxLayout="column">
  <div class="filter-container">
    <div class="filter-head" fxLayoutAlign="space-between start">
      <div fxLayout="row" fxLayoutGap="10px" class="search-container">
        <mat-form-field fxFlex="70" fxFlex.gt-md="90" appearance="none" class="search-filter">
          <input id="search" matInput placeholder="Search by Batch Id, Name or Description" #searchVal name="searchVal"
            [(ngModel)]="search" (keydown.enter)="getSearchValue(search)" />
          <mat-icon matPrefix class="search-icon" (click)="getSearchValue(search)">search</mat-icon>
          <mat-icon matSuffix class="remove-icon" (click)="resetSearch()" *ngIf="search">close</mat-icon>
        </mat-form-field>
      </div>

      <div class="btn-container" fxLayout="row" fxLayoutGap="10px">
        <mat-form-field appearance="none" class="date-range-filter">
          <mat-select [(value)]="selected" (selectionChange)="getDataForDate($event.value)">
            <div *ngFor="let option of datePickerOptions">
              <!-- Recent -->
              <mat-option *ngIf="option.value === 'recent'" value="recent">
                <div fxLayout="column" class="range-category" *ngIf="option.value === 'recent'" fxLayout="row">
                  <span> Recent</span> &nbsp;
                  <span class="date-range"> </span>
                </div>
              </mat-option>
              <!-- Last Week / Month / Quarter -->
              <mat-option [value]="option.value" *ngIf="
                  option.value !== 'recent' && option.value !== 'custom_range'
                ">
                <div fxLayout="column" class="range-category">
                  {{ option.display }}
                  <span class="date-range">
                    {{ option.start_date | date: "mediumDate" }} -
                    {{ currentDate | date: "mediumDate" }}</span>
                </div>
              </mat-option>
              <!-- Custom range  -->
              <mat-option *ngIf="option.value === 'custom_range'" value="custom_range" (click)="picker.open()"
                style="margin: 6px 0 10px 0">
                <div style="display: flex">
                  Custom Range &nbsp;
                  <span class="date-range" *ngIf="customStartDate && customEndDate">
                    {{ customStartDate | date: "mediumDate" }} -
                    {{ customEndDate | date: "mediumDate" }}</span>
                  <span fxLayout style="margin: 0 0 0 8px">
                    <mat-date-range-input [rangePicker]="picker" [min]="minDate" [max]="maxDate" style="display: none">
                      <input matStartDate #dateRangeStart [(ngModel)]="customStartDate" />
                      &nbsp;
                      <input matEndDate #dateRangeEnd [(ngModel)]="customEndDate" (dateChange)="
                          dateRangeChange(customStartDate, customEndDate)
                        " />
                    </mat-date-range-input>
                    <!-- date picker -->
                    <mat-datepicker-toggle matSuffix [for]="picker"></mat-datepicker-toggle>
                    <mat-date-range-picker #picker></mat-date-range-picker>
                  </span>
                  <!-- <span class="date-range" *ngIf="customStartDate && customEndDate">
                  {{ customStartDate | date: "mediumDate" }} -
                  {{ customEndDate | date: "mediumDate" }}</span> -->
                </div>
              </mat-option>
            </div>
          </mat-select>
          <div class="date-range-icon">
            <img src="assets/images/calender.svg" />
          </div>
        </mat-form-field>
        <button fxFlex.gt-md="50" mat-raised-button *ngIf="permissionsObject['add_batch']"
          class="upload-btn filled-btn-primary" (click)="toggleSideNav('uploadBatch', '')"
          matTooltip="Upload batch of SKUs to be auto-classified.
          " matTooltipPosition="above">
          <mat-icon class="upload-btn-icon">add</mat-icon> Upload New Batch
        </button>
        <button mat-button class="reset-btn filled-btn-primary" fxLayout fxLayoutAlign="center center"
        matTooltip="
        Clear search and filters" matTooltipPosition="above" (click)="reset()">
          Reset
        </button>
        <div class="view-messages-icon" matTooltip="
        View comments" matTooltipPosition="above" [routerLink]="'/comments'"
          [queryParams]="{ sub: SubscriptionID, origin: '/home' }">
          <!-- <div class="dot"></div> -->
          <img src="assets/images/message.svg" />
        </div>
      </div>
    </div>
  </div>

  <div class="table-wrapper" fxFlex="100" fxLayout="column" fxLayoutGap="20px" *ngIf="!dataLoading">
    <mat-tab-group #tabGroup (selectedTabChange)="tabChanged($event)" [selectedIndex]="selectedIndex">
      <mat-tab [label]="tabHeader" *ngFor="let tabHeader of tabList">
        <ng-template mat-tab-label>
          <span matTooltip="{{tabHeader.description}}"
            matTooltipPosition="above">{{ tabHeader.bucket | underscoreAsSpace | titlecase }} </span>&nbsp;
          <span class="batch-count" fxLayoutAlign="center center">
            {{ headercount && headercount[tabHeader.bucket] }}</span>
        </ng-template>
        <div class="mat-elevation-z8" class="table-section" style="margin-top: 12px;">
          <!-- table -->
          <table mat-table [dataSource]="homeDataSource">
            <!-- Position Column -->
            <ng-container matColumnDef="{{ item }}" *ngFor="let item of displayedColumns">
              <th mat-header-cell *matHeaderCellDef [ngClass]="{
                  'right-align':
                  item == 'ETA' ||
                  item == 'Actions' ||
                    item == 'Total Rows' ||
                    item == 'Accepted' ||
                    item == 'Others'
                }">
                <span>
                  {{ item }}
                </span>
              </th>
              <td mat-cell *matCellDef="let element">
                <div fxLayout="column" *ngIf="item == 'Batch ID'" style="margin-top: 10px">
                  <div fxLayout="row" fxLayoutAlign="start center">
                    <a [ngClass]="{
                        'disable-link': !permissionsObject['view_batch']
                      }" class="text-theme-primary" [routerLink]="['/products']" [queryParams]="{
                        batch_id: element.batch_id,
                        sub: SubscriptionID
                      }">
                      {{ element.batch_id }}
                    </a>
                    <span>
                      <mat-icon class="copy-icon" matTooltip="Copy text" matTooltipPosition="above"
                        [cdkCopyToClipboard]="element.batch_id" (cdkCopyToClipboardCopied)="showSnackbar()">content_copy
                      </mat-icon>
                    </span>
                  </div>
                  <p class="batch-date" style="margin-top: 0" *ngIf="element.reference_batch_id">
                    Ref: {{ element.reference_batch_id }}
                  </p>
                </div>
                <div fxLayout="column" *ngIf="item == 'Name & Description'">
                  <div fxLayout="row" fxLayoutAlign="start center">
                    <div fxLayout="column">
                      <p class="batch-name" matTooltip="{{ element.name }}" matTooltipPosition="above"
                        *ngIf="element.name">
                        {{ element.name | truncate: 10 }}
                      </p>
                      <p *ngIf="!element.name" style="color: #959191;">
                        No Title
                      </p>
                      <p class="description" *ngIf="element.description" matTooltip="{{ element.description }}"
                        matTooltipPosition="above">
                        {{ element.description | truncate: 15 }}
                      </p>
                    </div>
                    <div class="download-icon" *ngIf="element.input_file_download">
                      <a [href]="element.input_file_download" download target="_blank" matTooltip="Download input file"
                        matTooltipPosition="above">
                        <mat-icon class="download-batch active">arrow_downward</mat-icon>
                      </a>
                    </div>
                  </div>
                </div>
                <div class="tags-container" fxLayout="row" *ngIf="item == 'Tags'">
                  <mat-chip-list>
                    <mat-chip class="label-chip" *ngFor="let item of element.labels" [matTooltip]="item.name"
                      matTooltipPosition="above" [ngStyle]="{ 'background-color': item.colour }">
                      {{ item.name }}
                      <mat-icon class="remove-label-chip" (click)="removeLabel(element.batch_id, item.id, '')">close
                      </mat-icon>
                    </mat-chip>
                    <button class="add-label-btn stroked-btn-primary" fxLayoutAlign="center center"
                      (click)="toggleSideNav('addTag', element.batch_id)">
                      <mat-icon class="add-label-icon action-btn" matPrefix>add</mat-icon>
                    </button>
                  </mat-chip-list>
                  <!-- <div
                    *ngFor="let item of element[item]"
                    class="label-chip"
                    fxLayout="row"
                    fxLayoutAlign="start center"
                    [ngStyle]="{ 'background-color': '#F5CEBE' }"
                  >
                    <span>{{ item }}</span>
                  </div> -->
                </div>
                <!--  -->
                <div *ngIf="item == 'Created On'" fxLayout="row" class="batch-date" style="margin-right: 40px;">
                  <div *ngIf="element.created_at; else noData" fxLayout="column">
                    <span>
                      {{ element.created_at | date:'MMM d' }}
                    </span>
                    <span class="gray-theme">
                      {{ element.created_at | date:'EEE h:mm a' }}
                    </span>
                  </div>
                  <mat-icon (click)="
                      toggleSideNav('batchLog', '');
                      getLog('batch', element.batch_id)
                    " class="batch-info">info_outline</mat-icon>
                </div>
                <div fxLayout="row" *ngIf="item == 'ETA'" fxLayoutAlign="end center">
                  <span class="batch-date" *ngIf="element.eta; else noData">
                    {{ element.eta | date: 'MMM d, y, h:mm a' }}
                  </span>
                  <mat-form-field style="width: 0" class="mat-datetime-picker">
                    <input matInput [ngxMatDatetimePicker]="picker" [(ngModel)]="element.eta" [min]="today"
                      (dateChange)="modifyEta(element.batch_id, $event.value)">
                    <mat-datepicker-toggle matSuffix [for]="picker"
                      [matTooltip]="element.eta ? 'Modify ETA' : 'Add ETA'" matTooltipPosition="above">
                    </mat-datepicker-toggle>
                    <ngx-mat-datetime-picker [enableMeridian]="true" #picker></ngx-mat-datetime-picker>
                  </mat-form-field>
                </div>
                <div fxLayout="column" *ngIf="item == 'Total Rows'" class="right-align">
                  <p *ngIf="element.total_rows; else noData">
                    {{ element.total_rows }}
                  </p>
                </div>
                <div fxLayout="column" *ngIf="item == 'Accepted'" class="right-align">
                  <p *ngIf="element.accepted; else noData">
                    {{ element.accepted }}
                  </p>

                  <p class="sub-text">({{ element.accepted_percent }}%)</p>
                </div>
                <div fxLayout="column" *ngIf="item == 'Others'" class="right-align">
                  <p *ngIf="element.others; else noData">
                    {{ element.others }}
                  </p>
                  <p class="sub-text">({{ element.others_percent }}%)</p>
                </div>
                <div fxLayout="column" fxLayoutAlign="center center" *ngIf="item == 'Actions'" style="padding: 20px">
                  <div *ngIf="element.status == 'In_Progress'" class="progress-wrapper" fxLayout="row"
                    fxLayoutGap="5px">
                    <mat-progress-bar mode="determinate" [value]="element.progress_percent" fxFlex="90">
                    </mat-progress-bar>
                    <p class="progress-bar-value" fxFlex="10">
                      {{ element.progress_percent }}%
                    </p>
                  </div>

                  <div style="width: fit-content" fxLayoutAlign="center center" *ngIf="
                      element.status == 'In_Queue' &&
                      permissionsObject['cancel_batch']
                    " fxLayoutGap="5px">
                    <img style="cursor: pointer" src="assets/images/home-page/cancel-icon.svg" matTooltip="Cancel"
                      matTooltipPosition="above" class="svg_icon"
                      (click)="approveORcancel(element.batch_id, 'CANCELLED')" />
                    <!-- delete -->
                    <img style="cursor: pointer" src="assets/images/home-page/delete-icon.svg" matTooltip="Delete"
                      matTooltipPosition="above" class="svg_icon" (click)="deleteBatch(element.batch_id)" />
                  </div>
                  <p *ngIf="
                      element.status == 'Cancelled' &&
                      permissionsObject['cancel_batch']
                    " fxLayoutAlign="center center" class="batch-cancelled">
                    <img style="cursor: pointer" src="assets/images/home-page/delete-icon.svg" matTooltip="Delete"
                      matTooltipPosition="above" class="svg_icon" (click)="deleteBatch(element.batch_id)" />
                  </p>

                  <div fxLayout="column" class="action-btn">
                    <button *ngIf="
                        element.status == 'Processed' &&
                        permissionsObject['approve_batch']
                      " class="filled-btn-primary" fxLayoutAlign="center center" mat-button fxFlexAlign="center"
                      matTooltip="Approve the batch" matTooltipPosition="above"
                      (click)="updatebatchStatus(element.batch_id, 'APPROVED')">
                      Approve
                    </button>
                    <!-- <button *ngIf="element.status == 'In_Progress'" class="filled-btn-primary"
                      fxLayoutAlign="center center" mat-button fxFlexAlign="center"
                      (click)="updatebatchStatus(element.batch_id, 'CANCELLED')">
                      Cancel
                    </button> -->
                    <!-- <button *ngIf="element.status == 'Approved'" class="stroked-btn-primary"
                      fxLayoutAlign="center center" mat-button fxFlexAlign="center"
                      (click)="generateOutputFile(element.batch_id)">
                      Generate Output
                    </button> -->

                    <!-- <button
                    matTooltipPosition="above"
                    [matTooltip]="element.fileGenerationInProgress == true || (element.download?.in_progress === true && element.download?.percent_completed) ?
                                  element.download?.message +'('+  element.download?.percent_completed    +'%)'  : element.fileGenerationInProgress === true || (element.download?.in_progress === true && !element.download?.percent_completed) ?
                                  element.download?.message :'Download output file'"
                      *ngIf="(element.status == 'Approved' || element.status == 'Processed') && permissionsObject['download_batch']"
                      class="batch-download" fxLayoutAlign="center center" mat-button fxFlexAlign="center"
                      (click)="
                          downloadOutputFile(element.last_edited_at, element.last_generated_at, element.batch_id, element.download); element.fileGenerationInProgress = true;
                        ">
                      Download
                    </button> -->
                    <div>
                      <button *ngIf="
                          (element.status == 'Approved' ||
                            element.status == 'Processed') &&
                          permissionsObject['download_batch']
                        " class="batch-download filled-btn-without-border" fxLayoutAlign="center center" mat-button
                        fxFlexAlign="center" [matMenuTriggerFor]="module_status" #t="matMenuTrigger" mat-stroked-button
                        fxLayout="row" matTooltip="Download output file
                        " matTooltipPosition="above"
                        fxLayoutAlign="space-between center">
                        Download
                        <img [src]="
                            t.menuOpen
                              ? '../../assets/images/landing-page/up-arrow-white.svg'
                              : '../../assets/images/landing-page/down-arrow-white.svg'
                          " />
                      </button>
                      <mat-menu #module_status="matMenu">
                        <button mat-menu-item fxLayout="row" fxLayoutGap="5px" matTooltipPosition="above"
                        [matTooltip]="element.fileGenerationInProgress == true || (element.download?.in_progress === true && element.download?.percent_completed !== null ) ?
                                        toolTipMessage
                                        : element.fileGenerationInProgress === true || (element.download?.in_progress === true && !element.download?.percent_completed) ?
                                        element.download?.message :'Download output file'"
                                        (click)="
                            downloadOutputFile(
                              element.batch_id, 'ALL'
                            );

                            element.fileGenerationInProgress = true
                          ">
                          <span >Output File</span>
                        </button>

                        <button
                        *ngIf="element.insufficient_rows > 0"
                        mat-menu-item
                        fxLayout="row"
                        fxLayoutGap="5px"
                        matTooltip="View rows with insufficient data"
                        matTooltipPosition="above"
                        (click)="downloadOutputFile(element.batch_id, 'INSUFFICIENT_DATA')"
                      >
                        <span>Insufficient Rows</span>
                      </button>

                        <button *ngIf="element.asset_download !== null" mat-menu-item fxLayout="row" fxLayoutGap="5px"
                          (click)="downloadAsset(element.asset_download)">
                          <span>Assets</span>
                        </button>
                      </mat-menu>
                    </div>
                  </div>
                </div>
                <div fxLayout="column" *ngIf="item == 'Comments'">
                  <div class="comments-icon">
                    <div class="dot-comments" *ngIf="element.has_comments"></div>
                    <img [routerLink]="['/comments']" [queryParams]="{
                        origin: '/home',
                        batch_id: element.batch_id,
                        sub: SubscriptionID
                      }" src="assets/images/message.svg" />
                  </div>
                </div>
                <ng-template #noData></ng-template>
              </td>
            </ng-container>

            <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
            <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
          </table>
          <!-- progress spinner -->
          <div class="loading-spinner" fxLayoutAlign="center center" *ngIf="tableDataLoading">
            <mat-spinner fxLayoutAlign="center center" diameter="60" strokeWidth="3"></mat-spinner>
          </div>
          <!-- no data section -->
          <div class="no-data" *ngIf="batchList?.length == 0 && !tableDataLoading" fxLayout="row" fxLayoutGap="10px">
            <mat-icon>info</mat-icon>
            <span>Nothing to display.</span>
          </div>
          <mat-paginator [length]="totalItems" [pageSize]="size" [pageIndex]="page - 1" *ngIf="batchList?.length > 0"
            [pageSizeOptions]="[10, 20, 50, 100]" (page)="onPaginateChange($event)" showFirstLastButtons>
          </mat-paginator>
        </div>
      </mat-tab>
    </mat-tab-group>
  </div>

  <div class="data-loading-spinner" fxLayoutAlign="center center" *ngIf="dataLoading">
    <mat-spinner fxLayoutAlign="center center" diameter="90" strokeWidth="3"></mat-spinner>
  </div>
</div>

<ng-template #panelContent>
  <mat-progress-bar mode="determinate" class="file-upload-progress" [value]="uploadProgress" matTooltip="{{ uploadProgress | number: '1.0-0' }}%"
      matTooltipPosition="above" *ngIf="fileUploadStatus === 'Uploading'">
  </mat-progress-bar>
  <div *ngIf="uploadBatch" class="upload-batch-panel">
    <div fxLayout="row" fxLayoutAlign="space-between center">
      <span class="panel-header">Upload New Batch</span>
      <img class="batch-stop-icon" [ngClass]="{ 'disable-close': fileUploadStatus === 'Uploading' }" src="assets/images/home-page/close.svg"
        (click)="closeSidepanel()" />
    </div>
    <!-- drop box -->
    <ngx-dropzone (change)="onSelect($event)" [multiple]="false" [disabled]="fileUploadStatus == 'Uploading'"
      accept="text/csv, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel, application/wps-office.xlsx, application/zip, .xls, .xlsx, .csv, .zip, .txt"
      style="text-align: center">
      <ngx-dropzone-label fxLayout="column">
        <mat-icon fxLayoutAlign="space-between center">folder_open</mat-icon>
        Drag and Drop your files here
      </ngx-dropzone-label>
      <ng-container>
        <ngx-dropzone-preview *ngFor="let f of files" [removable]="true" (removed)="onRemove(f)">
          <ngx-dropzone-label [matTooltip]="f.name + ' ('+f.type+' )'" matTooltipPosition="above">{{ f.name | truncate: 25 }}</ngx-dropzone-label>
        </ngx-dropzone-preview>
      </ng-container>
    </ngx-dropzone>

    <!-- <p class="dropzone-info">
      Upload the data similar to the format specified in the
      <a class="sample-docs text-theme-primary">
        Sample Document
        <span>
          <img class="download-sample-doc" src="../../../assets/images/home-page/download.svg" class="svg_icon" />
        </span>
      </a>
    </p> -->

    <!-- description box -->
    <form class="upload-form" [formGroup]="uploadBatchForm" (ngSubmit)="upload()">
      <p>Batch Name*</p>
      <mat-form-field class="example-full-width" appearance="outline">
        <input matInput placeholder="Eg. Batch_104.csv" formControlName="name" [errorStateMatcher]="matcher" />
        <mat-error *ngIf="uploadBatchForm.controls['name'].hasError('required')">
          Batch name is required
        </mat-error>
        <mat-error *ngIf="uploadBatchForm.controls['name'].hasError('maxlength')">
          Batch name should not exceed 100 characters
        </mat-error>
        <mat-error *ngIf="uploadBatchForm.controls['name'].hasError('pattern') || uploadBatchForm.controls['name'].hasError('whitespace')">Please enter a valid batch name </mat-error>
      </mat-form-field>


      <!-- input format template IDs -->
      <p>Input Format*</p>
      <mat-form-field appearance="outline" class="file-format-dropdown">
        <mat-select formControlName="input_template_id">
          <mat-option [value]="template.template_id" *ngFor="let template of inputTemplates">{{template.name}}
          </mat-option>
        </mat-select>
      </mat-form-field>
      <!-- output format template IDs -->
      <p>Output Format*</p>
      <mat-form-field appearance="outline" class="file-format-dropdown">
        <mat-select formControlName="output_template_id">
          <mat-option [value]="template.template_id" *ngFor="let template of outputTemplates">{{template.name}}
          </mat-option>
        </mat-select>
      </mat-form-field>

      <p>Description</p>
      <mat-form-field class="example-full-width" appearance="outline">
        <textarea matInput rows="5" placeholder="Type your text here…" formControlName="description" [errorStateMatcher]="matcher" ></textarea>
        <mat-error *ngIf=" uploadBatchForm.controls['description'].hasError('maxlength')">
        Description should not exceed 2000 characters
      </mat-error>
      <mat-error *ngIf="uploadBatchForm.controls['description'].hasError('pattern') || uploadBatchForm.controls['description'].hasError('whitespace')">Please enter a valid description </mat-error>
      </mat-form-field>
      <p>Reference</p>
      <mat-form-field class="example-full-width" appearance="outline">
        <input matInput formControlName="reference" [errorStateMatcher]="matcher" />
        <mat-error *ngIf="uploadBatchForm.controls['reference'].hasError('maxlength')">
          Reference should not exceed 15 characters
        </mat-error>
        <mat-error *ngIf="uploadBatchForm.controls['reference'].hasError('pattern') || uploadBatchForm.controls['reference'].hasError('whitespace')">Please enter a valid reference </mat-error>
      </mat-form-field>





      <button [disabled]="
          fileUploadStatus === 'Uploading' || isUpload || files?.length == 0 || uploadBatchForm.invalid
        " mat-raised-button class="sidePanel-upload-btn filled-btn-without-border" fxLayout
        fxLayoutAlign="center center" type="submit">
        {{uploadButtonLabel}}
      </button>
    </form>
  </div>
  <!-- module chip -->

  <div *ngIf="addTag" class="upload-batch-panel">
    <div fxLayout="row" fxLayoutAlign="space-between start">
      <p class="panel-header">Add Tag</p>
      <img style="cursor: pointer" class="batch-stop-icon" src="assets/images/home-page/close.svg"
        (click)="closeSidepanel()" />
    </div>
    <div class="chip-container" fxLayout="row">
      <mat-chip-list selectable>
        <mat-chip *ngFor="let item of labelList" [matTooltip]="item.name" matTooltipPosition="above"
          [ngStyle]="{ 'background-color': item.colour }" (click)="addLabel(item.id, '')">
          {{ item.name }}
        </mat-chip>
      </mat-chip-list>
    </div>
    <hr />
    <form class="upload-form" [formGroup]="createLabelForm" (ngSubmit)="createLabel()">
      <p>Create New Tag</p>
      <mat-form-field class="example-full-width" appearance="outline">
        <input formControlName="name" maxlength="15" matInput placeholder="Type your text here..." />
        <mat-error *ngIf="createLabelForm.controls['name'].hasError('required')">
          Tag name is required
        </mat-error>
        <mat-error *ngIf="createLabelForm.controls['name'].hasError('pattern') || createLabelForm.controls['name'].hasError('whitespace')">Please enter a valid tag name </mat-error>
      </mat-form-field>
      <p>Description</p>
      <mat-form-field class="example-full-width" appearance="outline">
        <textarea formControlName="title" matInput rows="5" placeholder="Type your text here…"></textarea>
        <mat-error *ngIf="createLabelForm.controls['title'].hasError('required')">
          Tag description is required
        </mat-error>
        <mat-error *ngIf="createLabelForm.controls['title'].hasError('pattern') || createLabelForm.controls['title'].hasError('whitespace')">Please enter a valid description </mat-error>
      </mat-form-field>
      <!-- <div fxLayout="row" fxLayoutAlign="start start">
        <mat-chip-list selectable>
          <mat-chip class="tag-color-picker" *ngFor="let item of ['#96CEB4', '#FFEEAD', '#D9534F', '#FFAD60']"
            [ngStyle]="{ 'background-color': item }" (click)="toggleChip(item)" [selected]="chip.has(item)">
            <mat-icon class="iccon">done</mat-icon>
          </mat-chip>
        </mat-chip-list>
      </div> -->
      <p>Tag Color</p>
      <div fxLayout="row" fxLayoutAlign="start start">
        <div *ngFor="
            let item of ['#96CEB4', '#FFEEAD', '#D9534F', '#FFAD60'];
            let i = index
          ">
          <div class="color-palette" [ngStyle]="{ 'background-color': item }" (click)="toggleTagColor(i, item)">
            <mat-icon class="iccon" *ngIf="labelColor == item">done</mat-icon>
          </div>
        </div>
      </div>

      <!-- side panel upload button -->
      <button mat-raised-button class="sidePanel-upload-btn filled-btn-without-border" fxLayout
        fxLayoutAlign="center center" type="submit" [disabled]="createLabelForm.invalid || !labelColor">
        Create Tag
      </button>
    </form>
  </div>

  <div *ngIf="batchLog" class="upload-batch-panel">
    <div fxLayout="row" fxLayoutAlign="space-between start">
      <p class="panel-header">Batch Log</p>
      <img style="cursor: pointer" class="batch-stop-icon" src="assets/images/home-page/close.svg"
        (click)="closeSidepanel()" />
    </div>
    <mat-stepper orientation="vertical" [linear]="false" #stepper *ngIf="!logsLoading">
      <mat-step *ngFor="let log of logList" [completed]="false" [editable]="false">
        <ng-template matStepLabel>
          <span innerHTML="{{ log.text | highlightWord: log.id }}"></span>
        </ng-template>
      </mat-step>
    </mat-stepper>
    <div class="loading-spinner center-placement" fxLayoutAlign="center center" *ngIf="logsLoading">
      <mat-spinner fxLayoutAlign="center center" diameter="60" strokeWidth="3"></mat-spinner>
    </div>
  </div>
</ng-template>

<app-side-panel [sidenavTemplateRef]="panelContent" [direction]="'right'" [navWidth]="380" [duration]="0.5">
</app-side-panel>
