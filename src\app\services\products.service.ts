import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { Globals } from '../_globals/endpoints.global';
import { Observable, throwError } from 'rxjs';
import { map, catchError } from 'rxjs/operators';
import { ENDPOINTS } from '../_globals/endpoints';

@Injectable({
  providedIn: 'root',
})
export class ProductsService {
  private endpoints: any = ENDPOINTS;
  private httpOptions: HttpHeaders;
  constructor(private globals: Globals, private http: HttpClient) {
    this.httpOptions = new HttpHeaders({
      'Content-Type': 'application/json',
    });
  }

  /**
   * get product table headers
   * @param module_slug
   * @returns
   */
  getProductHeader = (subs_id) => {
    const productHeaderListEndpoint = this.globals.urlJoinWithParam(
      'products',
      'productHeaders',
      subs_id
    );
    return this.http.get(productHeaderListEndpoint).pipe(
      map((response: any) => {
        return response;
      }),
      catchError((error) => throwError(error))
    );
  };

  /**
   * get Products table
   * @param subs_id
   * @param page
   * @param size
   * @param bucket
   * @param search_query
   * @param start_date
   * @param end_date
   * @returns
   */
  getProductList = (
    subs_id,
    page,
    size,
    bucket,
    search_query,
    filter,
    start_date,
    end_date
  ): Observable<any> => {
    const options = {
      params: new HttpParams()
        .set('page', page)
        .set('page_size', size)
        .set('bucket', bucket)
        .set('start_date', start_date)
        .set('end_date', end_date),
    };
    search_query.forEach((item) => {
      options.params = options.params.append('q', item);
    });
    filter.forEach((item) => {
      options.params = options.params.append('filter', item);
    });
    const ProductListEndpoint = this.globals.urlJoinWithParam(
      'products',
      'productList',
      subs_id
    );
    return this.http.get(ProductListEndpoint, options).pipe(
      map((response: any) => {
        return response;
      }),
      catchError((error) => throwError(error))
    );
  };

  /**
   * each bucket count
   * @param subs_id
   * @returns
   */
  getBucketCount = (subs_id, search_query, filter, start_date, end_date) => {
    const bucketCountEndpoint = this.globals.urlJoinWithParam(
      'products',
      'bucketCount',
      subs_id
    );
    const options = {
      params: new HttpParams()
        .set('start_date', start_date)
        .set('end_date', end_date),
    };
    search_query.forEach((item) => {
      options.params = options.params.append('q', item);
    });
    filter.forEach((item) => {
      options.params = options.params.append('filter', item);
    });
    return this.http.get(bucketCountEndpoint, options).pipe(
      map((response: any) => {
        return response;
      }),
      catchError((error) => throwError(error))
    );
  };

  /**
   * get is_batch boolean and progress for each filter
   * @param module
   * @param filters
   * @returns
   */

  getBatchProgress = (subs_id, q): Observable<any> => {
    const batchProgressEndpoint = this.globals.urlJoinWithParam(
      'products',
      'batchProgress',
      subs_id
    );
    const options = {
      params: new HttpParams(),
    };
    q.forEach((item) => {
      options.params = options.params.append('q[]', item);
    });
    return this.http.get(batchProgressEndpoint, options).pipe(
      map((response: any) => {
        return response;
      }),
      catchError((error) => throwError(error))
    );
  };

  /**
   * move row to specific bucket
   * @param batchId
   * @param obj
   * @param subs_id
   * @returns
   */
  bucketUpdate = (rowId, obj, subs_id) => {
    const buckUpdateEndpoint = this.globals.urlJoinWithTwoParam(
      'products',
      'bucketUpdate',
      subs_id,
      rowId
    );
    return this.http.patch(buckUpdateEndpoint, obj, {}).pipe(
      map((response: any) => {
        return response;
      }),
      catchError((error) => throwError(error))
    );
  };

  bnulkBucketUpdate = (obj,batch_id,currentBucket, subs_id) => {
    const buckUpdateEndpoint = this.globals.urlJoinWithParam(
      'products',
      'bulkBucketUpdate',
      subs_id
    );
    const queryParams ={
      current_bucket:currentBucket
    }
    console.log(buckUpdateEndpoint);
    return this.http.patch(buckUpdateEndpoint, obj,{ params: queryParams}).pipe(
      map((response: any) => {
        return response;
      }),
      catchError((error) => throwError(error))
    );
  };
  bnulkBucketOptions = (obj,batch_id,currentBucket, subs_id) => {
    const buckUpdateEndpoint = this.globals.urlJoinWithParam(
      'products',
      'bulkBucketUpdate',
      subs_id
    );
    const queryParams ={
      current_bucket:currentBucket
    }
    console.log(buckUpdateEndpoint);
    return this.http.patch(buckUpdateEndpoint, obj,{ params: queryParams}).pipe(
      map((response: any) => {
        return response;
      }),
      catchError((error) => throwError(error))
    );
  };

  /**
   * category searche ndpoint
   * @param subs_id subscription id
   * @param q query param
   * @returns list of categories
   */
  attributeSearch = (subs_id, q) => {
    const attributeSearchEndpoint = this.globals.urlJoinWithParam(
      'products',
      'attributeSearch',
      subs_id
    );
    const options = {
      params: new HttpParams().set('search', q),
    };
    return this.http.get(attributeSearchEndpoint, options).pipe(
      map((response: any) => {
        return response;
      }),
      catchError((error) => throwError(error))
    );
  };

  /**
   * update category for product
   * @param subs_id
   * @param row_id
   * @param obj
   * @returns
   */
  categoryUpdate = (subs_id, row_id, obj) => {
    const categoryUpdateEndpoint = this.globals.urlJoinWithTwoParam(
      'products',
      'categoryUpdate',
      subs_id,
      row_id
    );
    return this.http.patch(categoryUpdateEndpoint, obj, {}).pipe(
      map((response: any) => {
        return response;
      }),
      catchError((error) => throwError(error))
    );
  };
  categoryUpdateBulkOption = (subs_id,currentBucket, batch_id, obj) => {
    const categoryUpdateEndpoint = this.globals.urlJoinWithParam(
      'products',
      'bulkCategoryUpdate',
      subs_id,

    );
    const queryParams ={
      q:batch_id,
      current_bucket:currentBucket
    }
    return this.http.patch(categoryUpdateEndpoint, obj,{ params: queryParams}).pipe(
      map((response: any) => {
        return response;
      }),
      catchError((error) => throwError(error))
    );
  };
}
