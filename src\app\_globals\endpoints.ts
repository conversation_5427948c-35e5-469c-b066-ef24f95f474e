const ENDPOINTS: any = {
  user: {
    me: {
      pathA: '/api/subscriptions/',
      pathB: '/me',
    },
    permissions: {
      pathA: '/api/subscriptions/',
      pathB: '/permissions',
    },
  },

  home: {
    batchList: '/api/subscriptions/',
    singleBatchList:'/api/subscriptions/',
    stats: '/api/subscriptions/',
    getTemplateId: {
      pathA: '/api/subscriptions/',
      pathB: '/file_formats',
    },
    labelList: '/api/subscriptions/',
    statusUpdate: '/api/subscriptions/',
    deleteBatch: '/api/subscriptions/',
    count: '/api/subscriptions/',
    generateOutput: '/api/subscriptions/',
    downloadOutputFile: '/api/subscriptions/',
    updateETA: '/api/subscriptions/',
    batchLog: {
      pathA: '/api/subscriptions/',
      pathB: '/log',
    },
  },
  file_upload: {
    get_signed_url: '/api/subscriptions/',
    upload_complete: '/api/subscriptions/',
  },
  review: {
    categoryFilter: '/api/subscriptions/',
    rowIndex: '/api/subscriptions/',
    acceptAllSuggestion: '/api/subscriptions/',
    reviewProductDetail: {
      pathA: '/api/subscriptions/',
      pathB: '/review/list',
    },
    reviewModeProductDetail: '/api/subscriptions/',
  },
  product_details: {
    prediction_list: '/api/subscriptions/',
    attributeList: '/api/subscriptions/',
    editPredictions: '/api/subscriptions/',
  },

  undo: {
    undoCall: '/api/subscriptions/',
  },

  products: {
    productList: {
      pathA: '/api/subscriptions/',
      pathB: '/inputs',
    },
    productHeaders: {
      pathA: '/api/subscriptions/',
      pathB: '/headers',
    },
    bucketCount: {
      pathA: '/api/subscriptions/',
      pathB: '/inputs/row_count',
    },
    batchProgress: {
      pathA: '/api/subscriptions/',
      pathB: '/batch_progress',
    },
    bucketUpdate: {
      pathA: '/api/subscriptions/',
      pathB: '/inputs/',
      pathC: '/bucket_update',
    },
    bulkBucketUpdate:{
      pathA: '/api/subscriptions/',
      pathB: '/input_row_bulk_bucket_update'
    },
    attributeSearch: {
      pathA: '/api/subscriptions/',
      pathB: '/choices',
    },
    categoryUpdate: {
      pathA: '/api/subscriptions/',
      pathB: '/inputs/',
      pathC: '/predictions/edit',
    },
    bulkCategoryUpdate: {
      pathA: '/api/subscriptions/',
      pathB: '/input_row_bulk_leaf_node_edit',
      pathC: '/predictions/edit',
    },

  },
  comments: {
    commentsList: {
      pathA: '/api/subscriptions/',
      pathB: '/comments',
    },
    postComment: {
      pathA: '/api/subscriptions/',
      pathB: '/comments/create',
    },
    userNamesToTag: {
      pathA: '/api/subscriptions/',
      pathB: '/usernames',
    },
    resolve: {
      pathA: '/api/subscriptions/',
      pathB: '/comments/resolve',
    },
    edit: {
      pathA: '/api/subscriptions/',
      pathB: '/comments/',
      pathC: '/edit',
    },
    delete: {
      pathA: '/api/subscriptions/',
      pathB: '/comments/',
      pathC: '/edit',
    },
  },
  settings: {
    apiUsage: {
      pathA: '/api/subscriptions/',
      pathB: '/api_usage',
    },
  },
  help: {
    feedback: {
      pathA: '/api/subscriptions/',
      pathB: '/feedback',
    },
  },
};
export { ENDPOINTS };
