import { Component, OnInit } from '@angular/core';
import {
  ActivatedRoute,
  Router,
  NavigationEnd,
  Event as NavigationEvent,
  NavigationStart,
} from '@angular/router';

@Component({
  selector: 'app-side-nav',
  templateUrl: './side-nav.component.html',
  styleUrls: ['./side-nav.component.scss'],
})
export class SideNavComponent implements OnInit {
  subscriptionId;
  event$;
  currentUrl;

  constructor(public router: Router) {}

  ngOnInit(): void {
    this.subscriptionId = localStorage.getItem('SubscriptionID');
    this.event$ = this.router.events.subscribe((event: NavigationEvent) => {
      if (event instanceof NavigationStart) {
        this.currentUrl = event.url;
      }
    });
  }

  // getUrl = () => {
  //   console.log(this.router.url);
  //   this.routeUrl = this.router.url;
  // };
}
