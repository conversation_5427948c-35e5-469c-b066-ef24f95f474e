import { Component, OnInit, Inject } from '@angular/core';
import { HttpErrorResponse } from '@angular/common/http';
import {
  MatSnackBar,
  MatSnackBarRef,
  MAT_SNACK_BAR_DATA,
} from '@angular/material/snack-bar';
import { UndoService } from '../../services/undo.service';

@Component({
  selector: 'app-undo-snackbar',
  templateUrl: './undo-snackbar.component.html',
  styleUrls: ['./undo-snackbar.component.scss'],
})
export class UndoSnackbarComponent implements OnInit {
  response: any;
  subscriptionId: any;
  Id: any;
  loading: boolean;

  constructor(
    public snackBarRef: MatSnackBarRef<UndoSnackbarComponent>,
    @Inject(MAT_SNACK_BAR_DATA)
    public data: any,
    private undoService: UndoService,
    private matSnackbar: MatSnackBar
  ) {}

  ngOnInit() {
    this.response = this.data.response;
    this.subscriptionId = this.data.subscriptionId;
    this.Id = this.data.Id;
  }

  undoCall = () => {
    this.loading = true;
    this.response = 'PROCESSING..';
    this.undoService.bulkUndo(this.subscriptionId, this.Id).subscribe({
      next: (resp) => {
        this.loading = false;
        this.response = resp.detail;
        this.data.onUndo();
        this.matSnackbar.open(resp.detail, 'OK', {
          duration: 3000,
        });
      },
      error: (HttpResponse: HttpErrorResponse) => {
        this.loading = false;
        this.response = this.data.response;
        this.matSnackbar.open(`${HttpResponse.error.detail}`, 'OK', {
          duration: 3000,
        });
      },
    });
  };
}
