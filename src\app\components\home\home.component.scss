@import "../../../styles/variables";
.file-upload-progress {
  position: fixed;
  z-index: 100;
}
.upload-batch-panel {
  padding: 20px 32px;
  hr {
    border: 1px solid #e6e8f0;
  }
  .panel-header {
    font-weight: 600;
    font-size: 16px;
    line-height: 24px;
  }
  .batch-stop-icon {
    cursor: pointer;
  }
  .disable-close {
    cursor: none;
    pointer-events: none;
  }
  ngx-dropzone {
    height: 120px;
    justify-content: center;
    align-items: center;
    border: 1px dashed #c1c4d6;
    border-radius: 4px;
    margin-bottom: 20px;
    margin-top: 10px;
    ngx-dropzone-label {
      font-family: $site-font;
      font-weight: 300;
      font-size: 12px;
      line-height: 18px;
      letter-spacing: 0px;
      color: $tabel-data;
      opacity: 1;
      mat-icon {
        margin-left: 60px;
      }
    }
    ngx-dropzone-preview {
      min-height: 80px !important;
      height: 80px !important;
    }
  }
  .dropzone-info {
    // margin-right: 100px;
    width: 340px;
    font-size: $site-font;
    font-weight: normal;
    font-size: 14px;
    line-height: 21px;
    color: #474d66;
    .sample-docs {
      font-weight: 600;
      font-size: 14px;
      line-height: 21px;
      img {
        padding-left: 5px;
        // margin-top: 10px;
        cursor: pointer;
      }
    }
  }

  .upload-form {
    p {
      font-weight: 600;
      font-size: 14px;
      color: $theme-black;
    }
    mat-form-field {
      width: 320px;
    }
   
    .sidePanel-upload-btn {
      // position: absolute;
      margin-top: 50px;
      bottom: 30px;
      width: 320px;
      height: 40px;
      border-radius: 4px;
      font-weight: 600;
    }
    .color-palette {
      height: 20px;
      width: 20px;
      margin-right: 5px;
      border-radius: 5px;
      margin-bottom: 20px;
      cursor: pointer;
    }
  }
  .chip-container {
    margin-top: 10px;
    margin-bottom: 20px;
    mat-chip {
      font-family: $site-font;
      font-weight: 300;
      cursor: pointer;
    }
  }
  .batch-log-id {
    font-weight: 600;
    padding: 5px;
  }
}
.comments-icon {
  cursor: pointer;
  .dot-comments {
    position: relative;
    height: 8px;
    width: 8px;
    border-radius: 50%;
    background-color: $theme-red;
    top: 5px;
    left: 16px;
  }
}
.disable-link {
  color: gray !important;
  pointer-events: none !important;
  text-decoration: none !important;
}
.right-align{
  text-align: right;
  padding-right: 20px;
}
.center-align{
  text-align: center;
  padding-right: 20px;
}

.download-icon {
  cursor: pointer;
  // margin-left: 10px;
  // right: 0;
  padding-left: 5px;
  padding-right: 3px;
  margin-left: auto; 
  margin-right: 15px;
  // margin-top: 10px;
  .download-batch {
    border-radius: 4px;
    color: white;
    font-size: 14px;
    padding: 2px;
    height: 15px;
    width: 15px;
  }
}// to avoid red border
::ng-deep .mat-form-field-appearance-outline.mat-form-field-invalid.mat-form-field-invalid .mat-form-field-outline-thick{
  color: #E6E8F0!important;
  opacity: 0.8!important;
}
.delete-icon {
  color: #fe4e34;
  border-radius: 4px;
}