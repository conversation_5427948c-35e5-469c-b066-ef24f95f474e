import { Injectable } from '@angular/core';
import { HttpHeaders, HttpClient, HttpParams } from '@angular/common/http';
import { Globals } from '../_globals/endpoints.global';
import { Observable, throwError } from 'rxjs';
import { map, catchError } from 'rxjs/operators';

@Injectable({
  providedIn: 'root',
})
export class HomeService {
  private httpOptions: HttpHeaders;
  // private endpoints: any = ENDPOINTS;
  constructor(private globals: Globals, private http: HttpClient) {
    this.httpOptions = new HttpHeaders({
      'Content-Type': 'application/json',
    });
  }

  /**
   *
   * @param response
   * get batch list
   */
  getBatchList = (
    page,
    size,
    status,
    search,
    start_date,
    end_date,
    subscription_id
  ): Observable<any> => {
    const options = {
      params: new HttpParams()
        .set('page', page)
        .set('page_size', size)
        .set('status', status)
        .set('q', search)
        .set('start_date', start_date)
        .set('end_date', end_date),
    };
    const BatchListEndpoint = this.globals.urlJoin('home', 'batchList');
    return this.http
      .get(BatchListEndpoint + subscription_id + '/batches' + '/list', options)
      .pipe(
        map((response: any) => {
          return response;
        }),
        catchError((error) => throwError(error))
      );
  };

  /**
 * Get single batch list
 * @param subscription_id
 * @param batch_id
 * @returns
 */
  getSingleBatchList = (
    subscription_id: string,
    batch_id: string,
    bucket_type: string
  ): Observable<any> => {
    const SingleBatchListEndpoint = this.globals.urlJoin(
      'home',
      'singleBatchList'
    );

    // Prepare the request body
    const body = { bucket: bucket_type };

    return this.http
      .post( SingleBatchListEndpoint+

        subscription_id +
        '/batches/'+
        batch_id+
        '/output_file_status', body)
      .pipe(
        map((response: any) => response),
        catchError((error) => throwError(error))
      );
  };
    /**
   * get template id for selected file format
   * @param subscription_id
   * @param type
   * @returns
   */
     getTemplateId = (subscription_id, type): Observable<any> => {
      const getTemplateIdEndpoint = this.globals.urlJoinWithParam(
        'home',
        'getTemplateId',
        subscription_id
      );
      const options = {
        params: new HttpParams().set('type', type),
      };
      return this.http.get(getTemplateIdEndpoint, options).pipe(
        map((response: any) => {
          return response;
        }),
        catchError((error) => throwError(error))
      );
    };


  /**
   * Get stats
   */
  getStats = (subscription_id): Observable<any> => {
    const StatsEndpoint = this.globals.urlJoin('home', 'stats');
    return this.http.get(StatsEndpoint + subscription_id + '/stats').pipe(
      map((response: any) => {
        return response;
      }),
      catchError((error) => throwError(error))
    );
  };

  /**
   * Get Labels
   */
  getLabelList = (subscription_id): Observable<any> => {
    const LabelListEndpoint = this.globals.urlJoin('home', 'labelList');
    return this.http
      .get(LabelListEndpoint + subscription_id + '/label_list')
      .pipe(
        map((response: any) => {
          return response;
        }),
        catchError((error) => throwError(error))
      );
  };

  /**
   * add label
   */
  addLabel = (
    subscription_id,
    batch_id,
    addlabel_id,
    removelabel_id
  ): Observable<any> => {
    const LabelEndpoint = this.globals.urlJoin('home', 'labelList');
    return this.http
      .patch(
        LabelEndpoint +
          subscription_id +
          '/batches/' +
          batch_id +
          '/add_remove_labels',
        {
          add_label: addlabel_id,
          remove_label: removelabel_id,
        }
      )
      .pipe(
        map((response: any) => {
          return response;
        }),
        catchError((error) => throwError(error))
      );
  };

  /**
   * status update
   */
  statusUpdate = (subscription_id, batch_id, status): Observable<any> => {
    const statusUpdateEndpoint = this.globals.urlJoin('home', 'statusUpdate');
    return this.http
      .patch(
        statusUpdateEndpoint +
          subscription_id +
          '/batches/' +
          batch_id +
          '/status_update',
        {
          status: status,
        }
      )
      .pipe(
        map((response: any) => {
          return response;
        }),
        catchError((error) => throwError(error))
      );
  };

  /**
   * Delete batch
   */
  deleteBatchList = (subscription_id, batch_id): Observable<any> => {
    const deleteBatchListEndpoint = this.globals.urlJoin('home', 'deleteBatch');
    return this.http
      .delete(
        deleteBatchListEndpoint +
          subscription_id +
          '/batches/' +
          batch_id +
          '/delete'
      )
      .pipe(
        map((response: any) => {
          return response;
        }),
        catchError((error) => throwError(error))
      );
  };

  // approve/cancel batch
  approveORcancel = (subscription_id, batch_id, statusStr): Observable<any> => {
    const approveORcancelEndpoint =
      '/api/subscriptions/' +
      subscription_id +
      '/batches/' +
      batch_id +
      '/status_update';
    return this.http
      .patch(approveORcancelEndpoint, {
        status: statusStr,
      })
      .pipe(
        map((response) => {
          return response;
        }),
        catchError((error) => throwError(error))
      );
  };

  /**
   * add label
   */
  createLabel = (subscription_id, name, title, color): Observable<any> => {
    const createLabelEndpoint = this.globals.urlJoin('home', 'labelList');
    return this.http
      .patch(createLabelEndpoint + subscription_id + '/create_label', {
        name: name,
        title: title,
        colour: color,
      })
      .pipe(
        map((response: any) => {
          return response;
        }),
        catchError((error) => throwError(error))
      );
  };

  /**
   * Get status count
   */
  getStatusCount = (
    subscription_id,
    search,
    start_date,
    end_date
  ): Observable<any> => {
    const countEndpoint = this.globals.urlJoin('home', 'count');
    const options = {
      params: new HttpParams()
        .set('q', search)
        .set('start_date', start_date)
        .set('end_date', end_date),
    };
    return this.http
      .get(countEndpoint + subscription_id + '/batches/status_count', options)
      .pipe(
        map((response: any) => {
          return response;
        }),
        catchError((error) => throwError(error))
      );
  };

  /**
   * generate output file
   * @param subscription_id
   * @param batch_id
   * @returns
   */

    generateOutputFile = (subscription_id, batch_id, bucket_type): Observable<any> => {
      const fileGenerateEndPoint = this.globals.urlJoin('home', 'generateOutput');
      const url = `${fileGenerateEndPoint}${subscription_id}/batches/${batch_id}/generate_output`;

      const body = { bucket : bucket_type};

      return this.http
        .post(url, body)
        .pipe(
          map((response: any) => {
            return response;
          }),
          catchError((error) => throwError(error))
        );
    };

  downloadFile = (subscription_id, batch_id, bucket_type): Observable<any> => {
    const outputFileEndpoint = this.globals.urlJoin(
      'home',
      'downloadOutputFile'
    );
    return this.http
    .get(
      `${outputFileEndpoint}${subscription_id}/batches/${batch_id}/${bucket_type}/download_output`
    )
      .pipe(
        map((response: any) => {
          return response;
        }),
        catchError((error) => throwError(error))
      );
  };

  /**
   * get batch/sku log
   * @param subscription_id
   * @param category
   * @param category_id
   * @returns
   */
  getLog = (subscription_id, category, category_id): Observable<any> => {
    const getLogDetailsEndpoint = this.globals.urlJoinWithParam(
      'home',
      'batchLog',
      subscription_id
    );
    const options = {
      params: new HttpParams()
        .set('category', category)
        .set('category_id', category_id),
    };
    return this.http.get(getLogDetailsEndpoint, options).pipe(
      map((response: any) => {
        return response;
      }),
      catchError((error) => throwError(error))
    );
  };

  /**
   * update ETA
   * @param subscription_id
   * @param batch_id
   * @param date
   * @returns
   */
  modifyETA = (subscription_id, batch_id, date): Observable<any> => {
    const updateETAEndPoint = this.globals.urlJoin('home', 'updateETA');
    return this.http
      .post(
        updateETAEndPoint +
          subscription_id +
          '/batches/' +
          batch_id +
          '/add_eta',
        {
          eta: date,
        }
      )
      .pipe(
        map((response: any) => {
          return response;
        }),
        catchError((error) => throwError(error))
      );
  };
}
