<div class="wrapper" fxFlex="100">
  <div
    class="filter-container"
    fxLayout="column"
    fxLayoutAlign="start space-between"
  >
    <div class="filter-head" fxLayoutAlign="space-between start">
      <div
        fxLayout="row"
        fxLayoutGap="10px"
        class="search-container"
        style="display: flex; flex-basis: 100%"
      >
        <mat-chip-list #chipList> </mat-chip-list>
        <mat-form-field fxFlex="20" appearance="none" class="search-filter">
          <input
            id="search"
            matInput
            placeholder="Search MPN, Batch ID, Row ID"
            [matChipInputFor]="chipList"
            [matChipInputSeparatorKeyCodes]="separatorKeysCodes"
            [matChipInputAddOnBlur]="addOnBlur"
            (matChipInputTokenEnd)="addQuery($event)"
            [(ngModel)]="search"
          />
          <mat-icon matPrefix class="search-icon" (click)="addQuery($event)"
            >search</mat-icon
          >
          <mat-icon
            matSuffix
            class="remove-icon"
            (click)="resetSearch()"
            *ngIf="search"
            >close</mat-icon
          >
          <mat-chip-list #chipList> </mat-chip-list>
        </mat-form-field>

        <div class="btn-container" fxLayout="row" fxLayoutGap="10px">
          <mat-form-field appearance="none" class="date-range-filter">
            <mat-select
              [(value)]="selected"
              (selectionChange)="getDataForDate($event.value)"
            >
              <div *ngFor="let option of datePickerOptions">
                <!-- Recent -->
                <mat-option *ngIf="option.value === 'recent'" value="recent">
                  <div
                    fxLayout="column"
                    class="range-category"
                    *ngIf="option.value === 'recent'"
                    fxLayout="row"
                  >
                    <span> Recent</span> &nbsp;
                    <span class="date-range"> </span>
                  </div>
                </mat-option>
                <!-- Last Week / Month / Quarter -->
                <mat-option
                  [value]="option.value"
                  *ngIf="
                    option.value !== 'recent' && option.value !== 'custom_range'
                  "
                >
                  <div fxLayout="column" class="range-category">
                    {{ option.display }}
                    <span class="date-range">
                      {{ option.start_date | date: "mediumDate" }} -
                      {{ currentDate | date: "mediumDate" }}</span
                    >
                  </div>
                </mat-option>
                <!-- Custom range  -->
                <mat-option
                  *ngIf="option.value === 'custom_range'"
                  value="custom_range"
                  (click)="picker.open()"
                >
                  <div
                    fxLayout="row"
                    class="range-category"
                    fxLayoutAlign="start center"
                  >
                    <span>Custom Range</span>
                    <span
                      class="date-range"
                      *ngIf="customStartDate && customEndDate"
                    >
                      {{ customStartDate | date: "mediumDate" }} -
                      {{ customEndDate | date: "mediumDate" }}</span
                    >
                    <span fxLayout style="margin: 0 0 0 8px">
                      <mat-date-range-input
                        [rangePicker]="picker"
                        [min]="minDate"
                        [max]="maxDate"
                        style="display: none"
                      >
                        <input
                          matStartDate
                          #dateRangeStart
                          [(ngModel)]="customStartDate"
                        />
                        &nbsp;
                        <input
                          matEndDate
                          #dateRangeEnd
                          [(ngModel)]="customEndDate"
                          (dateChange)="
                            dateRangeChange(dateRangeStart, dateRangeEnd)
                          "
                        />
                      </mat-date-range-input>
                      <!-- date picker -->
                      <mat-datepicker-toggle
                        matPrefix
                        [for]="picker"
                      ></mat-datepicker-toggle>
                      <mat-date-range-picker
                        class="custom-date-icon"
                        #picker
                      ></mat-date-range-picker>
                    </span>
                  </div>
                </mat-option>
              </div>
            </mat-select>
            <div class="date-range-icon">
              <img src="assets/images/calender.svg" />
            </div>
          </mat-form-field>
        </div>

        <mat-form-field
          appearance="none"
          class="product-filter"
          fxFlex="20"
          *ngFor="let item of filterList; let i = index"
        >
          <mat-select
            placeholder="{{ item.display_name }}"
            [(ngModel)]="selectedProducts[i]"
            (ngModelChange)="getProductSelection()"
            name="selectedProducts"
            multiple
          >
            <mat-option
              #mulVal
              *ngFor="let mulVals of item.values"
              [value]="mulVals"
            >
              {{ mulVals }}
            </mat-option>
          </mat-select>
        </mat-form-field>

        <button
          fxFlex="10"
          mat-button
          class="previous-btn"
          (click)="changeProduct('previousProduct')"
        >
          <mat-icon>west</mat-icon>
          Prev
        </button>
        <button
          fxFlex="10"
          mat-button
          class="previous-btn"
          (click)="changeProduct('nextProduct')"
        >
          Next
          <mat-icon>east</mat-icon>
        </button>
        <!-- reset button -->
        <button
          mat-button
          class="reset-btn filled-btn-primary"
          fxLayout
          fxLayoutAlign="center center"
          (click)="resetFilters()"
        >
          Reset
        </button>
        <div
          class="view-messages-icon"
          matTooltip="Comments"
          matTooltipPosition="above"
          [routerLink]="'/comments'"
          [queryParams]="{ sub: subscriptionId, origin: '/review' }"
        >
          <!-- <div class="dot"></div> -->
          <img src="assets/images/message.svg" />
        </div>
      </div>
    </div>
    <div class="chip-wrapper">
      <mat-chip
        class="search-chip"
        *ngFor="let item of searchedItems"
        [selectable]="selectable"
        [removable]="removable"
        (removed)="removeQuery(item)"
      >
        {{ item }}
        <mat-icon matChipRemove *ngIf="removable">cancel</mat-icon>
      </mat-chip>
    </div>
  </div>

  <div class="table-wrapper">
    <div class="review-tabel" fxLayout="column">
      <!-- product index -->
      <div
        *ngIf="currentItem && totalItems && totalItems > 0"
        class="product-count"
      >
        <span class="current-item">{{ currentItem }}</span>
        <span class="total-items">/ {{ totalItems }} </span>
      </div>

      <mat-toolbar class="review-header" fxLayoutAlign="space-between">
        <div class="head" fxFlex="40">Product Details</div>
        <div class="head" fxFlex="40">Suggestions & Edits</div>
        <div class="head" fxFlex="20">Comments</div>
      </mat-toolbar>
      <!-- no review data -->
      <div
        *ngIf="dataLoading"
        class="no-review-data"
        fxLayoutAlign="center center"
        fxFlex="100"
      >
        <mat-spinner
          fxLayoutAlign="center center"
          diameter="60"
          strokeWidth="3"
        ></mat-spinner>
      </div>

      <div
        fxLayout="column"
        class="review-viewport"
        *ngIf="reviewData?.length > 0 && !dataLoading"
      >
        <!-- <app-loadingspinner *ngIf="loadingData && !pageload"></app-loadingspinner> -->
        <mat-card
          fxLayoutAlign="start space-between"
          class="review-page-main-card"
          *ngFor="let item of reviewData; let i = index"
          fxLayoutGap="20px"
        >
          <!-- product details (rightmost section) -->
          <div
            class="product-details"
            fxFlex="33"
            fxLayout="column"
            fxLayoutGap="10px"
          >
            <!-- showing 2 images with count overlay -->
            <div
              fxLayout="row"
              *ngIf="!item.showAllImg || item.showAllImg == false"
            >
              <div
                class="product-images"
                *ngFor="let img of item.images | slice: 0:3; let j = index"
              >
                <span *ngIf="j < 2 && img.value">
                  <img src="{{ img.value }}" />
                </span>
                <small
                  class="tag active"
                  fxLayoutAlign="center center"
                  *ngIf="img.tag && j != 2"
                >
                  {{ img.tag }}
                </small>
                <span
                  class="overlay"
                  *ngIf="j == 2 && img.value"
                  (click)="item.showAllImg = true"
                >
                  <img src="{{ img?.value }}" />
                  <h3>{{ item.images.length - j }}+</h3>
                </span>
              </div>
            </div>
            <!-- showing all images-->
            <div fxLayout="row" *ngIf="item.showAllImg == true">
              <div class="product-images" *ngFor="let img of item.images">
                <span>
                  <img [src]="img.value" />
                  <small
                    class="tag active"
                    fxLayoutAlign="center center"
                    *ngIf="img.tag"
                  >
                    {{ img.tag }}
                  </small>
                </span>
              </div>
            </div>
            <!-- titles -->
            <div *ngFor="let title of item.title" class="title">
              <p
                class="product-caption text-theme-primary"
                [routerLink]="['/product-details']"
                [queryParams]="{
                  sub: subscriptionId,
                  row_id: item.row_id,
                  from: 'review',
                  bucket: 'REVIEW'
                }"
                *ngIf="title.type == 'CLICKABLE'"
              >
                {{ title.value }}
              </p>

              <p
                class="product-title text-theme-primary"
                *ngIf="title.type == 'TITLE'"
              >
                {{ title.value }}
              </p>

              <p class="ref-url" *ngIf="title.data_type == 'ASSET_LINK'">
                <span>References</span>
                <mat-icon
                  class="url-icon text-theme-primary"
                  (click)="openDialogReferenceURL(title.value)"
                  >link</mat-icon
                >
              </p>

              <p
                class="product-description"
                *ngIf="title.type == 'DESCRIPTION'"
                style="overflow-x: auto"
              >
                <span>{{
                  title.value.length > 100 ? (title.value | slice: 0:100) : ""
                }}</span>
                <span attr.id="dots-{{ i }}" *ngIf="title.value.length > 100"
                  >...</span
                >
                <span attr.id="more-{{ i }}" style="display: none">
                  {{ title.value | slice: 100:title.value.length }}
                </span>
                <span
                  *ngIf="title.value.length > 100"
                  (click)="viewMore($event, i)"
                  attr.id="moreBtn-{{ i }}"
                  class="view-more text-theme-primary"
                  style="cursor: pointer"
                >
                  View More</span
                >
              </p>
            </div>
            <!-- asset list (doc/mp3/mp4/svg) -->
            <div class="file-download" fxLayout="row">
              <div
                fxLayout="column"
                class="icon-container"
                *ngFor="let asset of item.assets"
              >
                <a
                  fxLayout
                  href="{{ asset.value }}"
                  target="_blank"
                  rel="noopener noreferrer"
                  *ngIf="asset.value != ''"
                >
                  <img
                    src="../../../assets/images/review-page/DOC.svg"
                    *ngIf="asset.type === 'DOC'"
                    matTooltip="{{ asset.caption }}"
                    [matTooltipPosition]="'above'"
                  />
                  <img
                    src="../../../assets/images/review-page/MP3.svg"
                    *ngIf="asset.type === 'AUDIO'"
                    matTooltip="{{ asset.caption }}"
                    [matTooltipPosition]="'above'"
                  />
                  <img
                    src="../../../assets/images/review-page/MP4.svg"
                    *ngIf="asset.type === 'VIDEO'"
                    matTooltip="{{ asset.caption }}"
                    [matTooltipPosition]="'above'"
                  />
                  <img
                    src="../../../assets/images/review-page/ZIP.svg"
                    *ngIf="asset.type === 'ZIP'"
                    matTooltip="{{ asset.caption }}"
                    [matTooltipPosition]="'above'"
                  />
                </a>
              </div>
            </div>
            <div class="action-btn" fxLayout="row" fxLayoutGap="10px">
              <!-- <button mat-button class="stroked-btn-primary" fxFlex="33">
                  Download Input Data
                </button> -->
              <button
                mat-button
                class="filled-btn-primary"
                fxFlex="50"
                (click)="requestRework(item.row_id, 'rework')"
              >
                Request Rework
              </button>
              <button
                mat-button
                class="stroked-btn-primary"
                fxFlex="50"
                (click)="acceptAllSuggestions(item, item.row_id, true)"
              >
                Accept All Suggestions
              </button>
            </div>
          </div>

          <!-- suggestion and edits section-->
          <div class="suggestion-edit-section" fxFlex="33">
            <div
              *ngIf="item.predictions.length == 0"
              fxLayoutAlign="center center"
              fxLayoutGap="5px"
              class="no-review-suggestions"
            >
              <mat-icon fontSet="material-icons-outlined">info</mat-icon>
              <span class="no-pred-found"> No Suggestions available </span>
            </div>
            <!-- suggestions & edit -->
            <div
              *ngFor="
                let pre of item.showAllSuggestions
                  ? item.predictions
                  : (item.predictions | slice: 0:defSuggestionDisplayCount);
                let j = index
              "
            >
              <p class="suggestion-card-label" *ngIf="pre.main_attribute">
                {{ pre.display_name }}
              </p>
              <p
                class="suggestion-card-label"
                *ngIf="!pre.main_attribute && j == item.main_attribute_count"
              >
                ATTRIBUTES
              </p>
              <mat-card
                class="suggestion-edit-card"
                tabindex="0"
                [ngClass]="{
                  'active-selection': pre.display_name == item.activeAttribute
                }"
                (click)="item.activeAttribute = pre.display_name"
              >
                <div fxLayout class="attribute-name">
                  <p *ngIf="pre.main_attribute == false">
                    {{ pre.display_name }}
                  </p>
                </div>
                <!-- suggestions & edit value -->
                <div class="existing-section">
                  <p class="attr-existing">Existing</p>
                  <p class="attr-value" *ngFor="let e of pre.existing_values">
                    {{ e }}
                  </p>
                  <!-- suggestion -->
                  <div *ngIf="pre.suggestion_values.length > 0">
                    <div class="hr-line"></div>
                    <p class="attr-suggestion">Suggestion</p>
                    <div
                      *ngFor="
                        let suggestion of pre.suggestion_values;
                        let i = index
                      "
                    >
                      <!-- Accept / Reject -->
                      <div
                        fxLayout
                        class="action-btn"
                        fxLayoutGap="10px"
                        fxLayout="row"
                      >
                        <img
                          (click)="
                            updateSuggestion(
                              item,
                              pre,
                              item.row_id,
                              suggestion,
                              'accept',
                              true
                            )
                          "
                          src="../../../assets/images/review-page/accept.svg"
                        />
                        <img
                          (click)="
                            updateSuggestion(
                              item,
                              pre,
                              item.row_id,
                              suggestion,
                              'reject',
                              false
                            )
                          "
                          src="../../../assets/images/review-page/reject.svg"
                        />
                      </div>
                      <p
                        (click)="
                          updateSuggestion(
                            item,
                            pre,
                            item.row_id,
                            suggestion,
                            'accept',
                            false
                          )
                        "
                        class="attr-value"
                      >
                        {{ suggestion }}
                      </p>
                    </div>
                  </div>
                </div>
              </mat-card>
            </div>
            <button
              mat-button
              class="more-suggestions-btn"
              *ngIf="
                !item.showAllSuggestions &&
                item.predictions.length > defSuggestionDisplayCount
              "
              (click)="item.showAllSuggestions = true"
            >
              {{ item.predictions.length - defSuggestionDisplayCount }} More
              Suggestions
              <mat-icon>arrow_drop_down</mat-icon>
            </button>
          </div>

          <!-- no comments -->
         
          <!-- comment thread scroll container -->
          <div
            class="review-comments"
            fxFlex="30"
            #scrollContainer
            fxLayout="column"
            fxLayoutGap="20px"
            fxLayoutAlign="start center"
            infiniteScroll
            [scrollWindow]="false"
            [infiniteScrollDistance]="1"
            [infiniteScrollUpDistance]="1"
            [infiniteScrollThrottle]="50"
            (scrolled)="onCommentThreadScroll()"
          >
            <div
              fxLayout="row"
              class="cmt-profile"
              *ngFor="let comment of commentThread; let i = index"
              fxLayoutAlign="center start"
            >
              <!-- comment profile picture -->
              <img src="{{ comment.profile_picture }}" />
              <div fxLayout="column" class="cmt-profile-picture">
                <!-- comment header -->
                <div
                  fxLayout="row"
                  class="comment-head"
                  fxLayoutAlign="space-between center"
                >
                  <div fxLayout="column">
                    <p class="username">{{ comment.created_by }}</p>
                    <p class="comment-time">
                      {{ comment.created_at | date: "EEEE, MMMM d, y" }}
                    </p>
                  </div>
                  <div *ngIf="userData.username == comment.created_by_username">
                    <img
                      src="../../../assets/images/review-page/more.svg"
                      [matMenuTriggerFor]="menu"
                    />
                    <mat-menu #menu="matMenu">
                      <button
                        (click)="editComment(comment); comment.editable = true"
                        mat-menu-item
                      >
                        Edit
                      </button>
                      <button
                        (click)="deleteComment(comment.comment_id, i)"
                        mat-menu-item
                      >
                        Delete
                      </button>
                    </mat-menu>
                  </div>
                </div>

                <!-- comment content -->
                <div
                  class="comments"
                  fxLayoutAlign="space-between center"
                  fxFlex="100"
                >
                  <span style="width: 80%" *ngIf="!comment.editable"
                    ><span
                      class="tagged-users text-theme-primary"
                      *ngFor="let user of comment.tagged_users"
                      >@{{ user.name }}</span
                    >
                    {{ comment.text }}</span
                  >
                </div>
                <!-- edit comment part -->
                <div
                  class="edit-comment-box"
                  fxLayout="row"
                  *ngIf="comment.editable"
                  fxLayoutAlign="space-between center"
                >
                  <!-- text area -->
                  <textarea
                    matInput
                    name="message"
                    class="edit-comment-text"
                    [(ngModel)]="comment.edited_text"
                    #message="ngModel"
                    #editCommentRef
                  ></textarea>
                  <!-- tag configuration -->
                  <flx-mentions
                    [textInputElement]="editCommentRef"
                    [menuTemplate]="menuTemplate"
                    [triggerCharacter]="'@'"
                    [selectedChoices]="getSelectedChoices(comment)"
                    [getChoiceLabel]="getChoiceLabel"
                    (search)="loadChoices($event)"
                    (choiceRemoved)="taggedUserRemovedInCommentEdit($event)"
                    (selectedChoicesChange)="
                      onTaggedUserEdited($event, comment)
                    "
                  >
                  </flx-mentions>
                  <!-- tag template-->
                  <ng-template #menuTemplate let-selectChoice="selectChoice">
                    <ul class="flx-selectable-list">
                      <li
                        *ngFor="let user of choices"
                        class="flx-selectable-list-item"
                        (click)="selectChoice(user)"
                      >
                        <span title="{{ user.name }}">{{ user.name }}</span>
                      </li>
                    </ul>
                  </ng-template>
                  <!-- update and cancel -->
                  <div
                    fxLayout="row"
                    fxLayoutGap="5px"
                    fxLayoutAlign="space-between center"
                  >
                    <img
                      fxLayout="column"
                      class="send-icon"
                      src="../../../assets/images/review-page/comment-icon.svg"
                      (click)="updateComment(comment)"
                    />
                    <mat-icon (click)="cancelEdit(comment)">cancel</mat-icon>
                  </div>
                </div>
              </div>
            </div>
            <!-- comment area -->
            <div class="comment-box" fxLayout="row">
              <div
                fxLayout="row"
                class="cmt-textarea"
                fxLayoutAlign="space-between center"
              >
                <!-- text area -->
                <textarea
                  matInput
                  placeholder="Comment or add others with @"
                  [(ngModel)]="comments[activeCommentIndex]"
                  #textareaRef
                ></textarea>
                <!-- tag configuration -->
                <flx-mentions
                  [textInputElement]="textareaRef"
                  [menuTemplate]="tagTemplate"
                  [triggerCharacter]="'@'"
                  [getChoiceLabel]="getChoiceLabel"
                  (search)="loadChoices($event)"
                  (selectedChoicesChange)="onSelectedChoicesChange($event)"
                >
                </flx-mentions>
                <!-- tag template -->
                <ng-template #tagTemplate let-selectChoice="selectChoice">
                  <ul class="flx-selectable-list">
                    <li
                      *ngFor="let user of choices"
                      class="flx-selectable-list-item"
                      (click)="selectChoice(user)"
                    >
                      <span title="{{ user.name }}">{{ user.name }}</span>
                    </li>
                  </ul>
                </ng-template>
                <!-- post comment -->
                <img
                  fxLayout="column"
                  class="send-icon"
                  src="../../../assets/images/review-page/comment-icon.svg"
                  (click)="
                    postComment(
                      comments[activeCommentIndex],
                      activeCommentIndex
                    )
                  "
                />
              </div>
            </div>
            <div
            class="loading-comments"
            fxLayoutAlign="center center"
            *ngIf="commentsLoading && commentThread.length == 0"
            fxFlex="100"
          >
            <mat-spinner
              fxLayoutAlign="center center"
              diameter="40"
              strokeWidth="3"
            ></mat-spinner>
          </div>
          </div>
        </mat-card>
      </div>
      <!-- no data section -->
      <div
        class="no-data"
        *ngIf="reviewData?.length == 0 && !dataLoading"
        fxLayout="row"
        fxLayoutGap="10px"
      >
        <mat-icon>info</mat-icon>
        <span>Nothing to display.</span>
      </div>
    </div>
  </div>
</div>
