{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "cli": {"analytics": false}, "version": 1, "newProjectRoot": "projects", "projects": {"moduleR2A": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss"}, "@schematics/angular:application": {"strict": true}}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:browser", "options": {"outputPath": "dist/moduleR2A", "index": "src/index.html", "main": "src/main.ts", "polyfills": "src/polyfills.ts", "tsConfig": "tsconfig.app.json", "inlineStyleLanguage": "scss", "assets": ["src/favicon.ico", "src/assets"], "styles": ["./node_modules/@angular/material/prebuilt-themes/indigo-pink.css", "src/styles/styles.scss", "src/styles/mat-theme.scss"], "scripts": ["node_modules/chart.js/dist/chart.min.js"]}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "500kb", "maximumError": "3mb"}, {"type": "anyComponentStyle", "maximumWarning": "10kb", "maximumError": "20kb"}], "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}], "outputHashing": "all"}, "dev": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.dev.ts"}]}, "stag": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.stag.ts"}]}, "development": {"buildOptimizer": false, "optimization": false, "vendorChunk": true, "extractLicenses": false, "sourceMap": true, "namedChunks": true}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "options": {"browserTarget": "moduleR2A:build", "proxyConfig": "proxy.conf.json"}, "configurations": {"production": {"browserTarget": "moduleR2A:build:production"}, "development": {"browserTarget": "moduleR2A:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"browserTarget": "moduleR2A:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "src/test.ts", "polyfills": "src/polyfills.ts", "tsConfig": "tsconfig.spec.json", "karmaConfig": "karma.conf.js", "inlineStyleLanguage": "scss", "assets": ["src/favicon.ico", "src/assets"], "styles": ["./node_modules/@angular/material/prebuilt-themes/indigo-pink.css", "src/styles.scss"], "scripts": []}}}}}, "defaultProject": "moduleR2A"}