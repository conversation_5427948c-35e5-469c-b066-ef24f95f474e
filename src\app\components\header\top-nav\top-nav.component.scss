@import "../../../../styles/variables";
/* Top Navigation*/
.top-nav {
  position: fixed;
  left: 0;
  right: 0;
  top: 0;
  z-index: 999;
  padding: 0 90px;
  height: 70px;
  // margin-bottom: 20px;
  background: $theme-blue;
  box-shadow: 5px 5px 20px rgba(0, 0, 0, 0.12);
  .top-nav-logo {
    cursor: pointer;
  }
  &__menu-user {
    margin-left: 20px;
    font-family: $site-font;
    color: $theme-white;
    text-align: left;
    letter-spacing: 0px;
    opacity: 1;
    .display-name {
      font-weight: bolder;
      margin-top: 0;
    }
    .role {
      margin-top: -25px;
      font: normal normal normal 10px/49px Poppins;
    }
  }
  &__menu-notification {
    width: 32px;
    height: 32px;
    margin-top: 20px;
    margin-left: 15px;
  }
  &__menu-drop {
    left: 1176px;
    width: 10px;
    height: 5px;
  }
}
.module-menu {
  border: 1px solid #e6e8f0;
}
::ng-deep.mat-menu-panel {
  max-width: fit-content !important;
}
::ng-deep.mat-menu {
  max-width: fit-content !important;
  height: fit-content;
}
::ng-deep .notification-drop-down {
  height: fit-content;
  background: #ffffff;
  border: 1px solid #e6e8f0;
  box-sizing: border-box;
  box-shadow: 0px 3px 15px rgba(34, 35, 41, 0.05);
  border-radius: 4px;
  .notification-header {
    padding: 10px;
    p {
      font-family: $site-font;
      font-style: normal;
      font-weight: 600;
      font-size: 14px;
      line-height: 21px;
    }
    .header {
      color: $theme-black;
      margin-left: 10px;
    }
    .mark-read {
      cursor: pointer;
    }
  }
  .notification-item {
    width: auto;
    height: fit-content;
    img {
      width: 48px;
      height: 48px;
      border-radius: 15px;
      padding: 10px;
    }
    p {
      font-family: $site-font;
      font-style: normal;
      font-size: 14px;
      line-height: 21px;
    }
    .notification-disply-name {
      font-weight: 600;
      margin-top: 10px;
    }
    .notification-message {
      font-weight: normal;
      margin-top: -5px;
      color: #3b3e48;
    }
    .notification-time {
      font-weight: 300;
      font-size: 12px;
      line-height: 18px;
      margin-top: 5px;
      padding-left: 20px;
      text-align: right;

      color: #a2a5af;
    }
  }
}
.menu-item {
  width: 51px;
  height: 18px;
  text-align: left;
  text-align: left;
  font-family: $site-font;
  letter-spacing: 0px;
  color: #343434;
  opacity: 1;
}
.menu-drop {
  margin-top: 5px;
  margin-left: 10px;
  color: $theme-white;
  cursor: pointer;
}
.avatar {
  height: 40px;
  width: 40px;
  margin-left: 40px;
  border-radius: 4px;
  object-fit: cover;
}
.dot {
  position: fixed;
  height: 8px;
  width: 8px;
  background-color: $theme-red;
  border-radius: 50%;
  margin-left: 15px;
  margin-top: -8px;
}
mat-icon {
  color: $theme-white;
  cursor: pointer;
}
.client_logo {
  padding: 5px;
  height: 40px;
  background: #f4f6fa;
  border-radius: 4px;
  margin-left: 40px;
  img {
    width: 100px;
    height: 40px;
    object-fit: contain;
  }
}
