import { Component, OnInit } from '@angular/core';
import { timer, Subscription } from 'rxjs';
import { Location } from '@angular/common'
import { Router, ActivatedRoute, Params } from '@angular/router';
@Component({
  selector: 'app-offline',
  templateUrl: './offline.component.html',
  styleUrls: ['./offline.component.scss'],
})
export class OfflineComponent implements OnInit {
  getTimer: Subscription;
  SubscriptionID;
  route;

  constructor(private location: Location ,
    private activatedRoute: ActivatedRoute,
    private router: Router ) {}

  ngOnInit() {
    this.SubscriptionID =  localStorage.getItem("SubscriptionID");


    const ti = timer(0, 3000);
    this.getTimer = ti.subscribe((t) => {
      if (navigator.onLine) {
        this.router.navigate(['/home'], { queryParams: { sub: this.SubscriptionID } })
      }
    });

  }
}
