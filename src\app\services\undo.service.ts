import { Injectable } from '@angular/core';
import { Observable, throwError } from 'rxjs';
import { map, catchError } from 'rxjs/operators';
import { HttpHeaders, HttpClient } from '@angular/common/http';
import { ENDPOINTS } from '../_globals/endpoints';
import { Globals } from '../_globals/endpoints.global';

@Injectable({
  providedIn: 'root',
})
export class UndoService {
  private httpOptions: HttpHeaders;
  private endpoints: any = ENDPOINTS;
  values: any;
  constructor(private globals: Globals, private http: HttpClient) {
    this.httpOptions = new HttpHeaders({
      'Content-Type': 'application/json',
    });
  }

  /**
   * Undo edited prediction
   * @param row_id,subscriptionId,prediction_id
   * @body input
   */
  undo = (subscriptionId, row_id): Observable<any> => {
    const UndoEndpoint = this.globals.urlJoin('undo', 'undoCall');
    return this.http
      .post(UndoEndpoint + subscriptionId + '/inputs/' + row_id + '/undo', {})
      .pipe(
        map((response: any) => {
          return response;
        }),
        catchError((error) => throwError(error))
      );
  };

   /**
   * Undo edited prediction
   * @param row_id
   * @param subscriptionId,
   * @param payload
   * @body input
   */

  bulkUndo = (payload, subscriptionId): Observable<any> => {
    const url = this.globals.urlJoin('undo', 'undoCall');
    return this.http
    .post(url + subscriptionId + '/inputs/bulk_undo', payload)
    .pipe(
      map((response: any) => response),
      catchError((error) => throwError(error))
    );
  };
}

