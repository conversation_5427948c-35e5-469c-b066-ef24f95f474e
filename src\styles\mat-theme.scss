@use "@angular/material" as mat;
@import "~@angular/material/theming";
@import "./variables"; // contains custom palette
@import "./custom-palettes";

$app-typography: mat-typography-config(
  $font-family: "Poppins",
);

@include mat.core($app-typography);

// default app theme
$cax-default-primary: mat.define-palette($cax-default-primary);
$cax-default-accent: mat.define-palette($cax-default-primary);
$cax-default-theme: mat.define-light-theme(
  (
    color: (
      primary: $cax-default-primary,
      accent: $cax-default-accent,
    ),
  )
);

// custom mixin to change misc component styles/theme
// accepts theme
@mixin mix-app-theme($theme) {
  $primary: map-get($theme, primary);
  $accent: map-get($theme, accent);
  // app toggle buttons
  mat-button-toggle {
    color: mat-color($primary) !important;
    border: 1px solid mat-color($primary);
  }

  .mat-button-toggle-checked {
    background-color: mat-color($primary) !important;
    color: $theme-white !important;
  }

  .search-chip {
    border: 1px solid mat-color($primary) !important;
    color: mat-color($primary) !important;
    mat-icon {
      color: mat-color($primary) !important;
    }
  }

  // active route icon in side bar
  .active {
    background-color: mat-color($primary);
  }
  .active-text {
    color: mat-color($primary);
  }
  // theme colored text
  .text-theme-primary {
    color: mat-color($primary);
  }
  // tab label and inkbar
  .mat-tab-labels .mat-tab-label-active {
    color: mat-color($primary) !important;
  }

  .tab-container {
    .mat-tab-labels .mat-tab-label-active {
      color: #fff !important;
      background-color: mat-color($primary) !important;
    }
  }
  .stroked-btn-without-border {
    color: mat-color($primary);
    background-color: mat-color($primary);
  }
  .filled-btn-primary {
    background-color: mat-color($primary);
    border: 1px solid mat-color($primary) !important;
    color: #fff;
  }
  .filled-btn-without-border {
    background-color: mat-color($primary);
    color: #fff;
  }
  .stroked-btn-primary {
    border: 1px solid mat-color($primary) !important;
    color: mat-color($primary);
  }
  .chip {
    border: 1px solid mat-color($primary) !important;
    background-color: mat-color($primary);
  }
  .mat-option.mat-active {
    color: mat-color($primary);
  }
  .mat-option:hover {
    color: mat-color($primary);
  }
}

@include mat.all-component-themes($cax-default-theme);
// Include the mixin with default theme
@include mix-app-theme($cax-default-theme);

// client AD
$ad-app-primary: mat.define-palette($brand-ad-primary);
$ad-app-accent: mat.define-palette($brand-ad-primary);
$ad-app-theme: mat.define-light-theme(
  (
    color: (
      primary: $ad-app-primary,
      accent: $ad-app-accent,
    ),
  )
);

.ad-app-theme {
  @include angular-material-color($ad-app-theme);
  @include mix-app-theme($ad-app-theme);
}
