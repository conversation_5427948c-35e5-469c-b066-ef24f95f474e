<div class="app-wrapper" fxLayout="column" fxLayoutAlign="100"  (window:resize)="onResize($event)">
    <app-loading *ngIf="(auth.isLoading$ | async) && !appLoaded; else loaded"></app-loading>
    <ng-template #loaded>
    <div *ngIf="isLowerResolution == false">
      <!-- top nav -->
      <app-top-nav *ngIf="subscriptionId && router.url !== '/offline'"></app-top-nav>
      <!-- side Navigation -->
      <app-side-nav *ngIf="subscriptionId  && router.url !== '/offline'"></app-side-nav>
      <!-- other components rendering container -->
      <div class="content" layout="row" fxFlex="100">
        <div *ngIf="!permissionsAvailable" style="height: 100vh;" fxLayoutAlign="center center">
          <mat-spinner fxLayoutAlign="center center" diameter="90" strokeWidth="3"></mat-spinner>
        </div>
        <router-outlet></router-outlet>
      </div>
    </div>
    <div
    class="resolutionDevice"
    *ngIf="isLowerResolution == true"
    fxFlexLayout="center center"
  >
    <h1>
      <mat-icon class="not-supported-icon">announcement</mat-icon>Not
      Supported for Current resolution
    </h1>
  </div>
</ng-template>
  </div>